import uuid

from sqlmodel import Session, select

from app.models.review import Review
from app.schemas.review import ReviewCreate


class ReviewService:
    """Business logic for review operations."""
    
    def create_review(self, session: Session, *, user_id: uuid.UUID, review_in: ReviewCreate) -> Review:
        """Create a new review."""
        db_obj = Review(user_id=user_id, **review_in.model_dump())
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
    
    def get_reviews_for_coach(
        self, session: Session, *, coach_profile_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[Review]:
        """Get reviews for a specific coach."""
        statement = (
            select(Review)
            .where(Review.coach_profile_id == coach_profile_id)
            .order_by(Review.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
