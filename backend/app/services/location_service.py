import uuid

from sqlmodel import Session, select

from app.models.location import Location


class LocationService:
    """Business logic for location operations."""
    
    def get_all_locations(self, session: Session, *, skip: int = 0, limit: int = 100) -> list[Location]:
        """Get all locations."""
        statement = select(Location).offset(skip).limit(limit)
        return session.exec(statement).all()
    
    def get_location_by_id(self, session: Session, *, location_id: uuid.UUID) -> Location | None:
        """Get location by ID."""
        statement = select(Location).where(Location.id == location_id)
        return session.exec(statement).first()
    
    def create_location(self, session: Session, *, city: str, state: str, country: str) -> Location:
        """Create a new location."""
        db_obj = Location(city=city, state=state, country=country)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
