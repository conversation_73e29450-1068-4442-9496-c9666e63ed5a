from fastapi import APIRouter

from app.api.routes import bookings, coach_availability, coaches, dashboard, items, login, payments, private, users, utils
from app.core.config import settings

api_router = APIRouter()
api_router.include_router(login.router)
api_router.include_router(users.router)
api_router.include_router(utils.router)
api_router.include_router(items.router)
api_router.include_router(coaches.router, prefix="/coaches", tags=["coaches"])
api_router.include_router(bookings.router, prefix="/bookings", tags=["bookings"])
api_router.include_router(coach_availability.router, prefix="/availability", tags=["coach-availability"])
api_router.include_router(payments.router, prefix="/payments", tags=["payments"])
api_router.include_router(dashboard.router, tags=["dashboard"])


if settings.ENVIRONMENT == "local":
    api_router.include_router(private.router)
