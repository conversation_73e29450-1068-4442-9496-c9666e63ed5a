from fastapi import APIRouter

from app.api.deps import <PERSON><PERSON><PERSON><PERSON>, Coach<PERSON><PERSON>, CurrentUser
from app.models import Message, UserPublic

router = APIRouter(tags=["roles"])


@router.get("/roles/me", response_model=UserPublic)
def get_current_user_role(current_user: CurrentUser) -> UserPublic:
    """
    Get current user with role information.
    Accessible by any authenticated user.
    """
    return current_user


@router.get("/roles/coach-only", response_model=Message)
def coach_only_route(current_user: CoachUser) -> Message:
    """
    Coach-only endpoint.
    Only accessible by users with role 'coach' or 'admin'.
    """
    return Message(
        message=f"Hello, Coach {current_user.full_name or current_user.email}!"
    )


@router.get("/roles/admin-only", response_model=Message)
def admin_only_route(current_user: AdminUser) -> Message:
    """
    Admin-only endpoint.
    Only accessible by users with role 'admin'.
    """
    return Message(
        message=f"Hello, Admin {current_user.full_name or current_user.email}!"
    )


@router.get("/roles/user-info", response_model=Message)
def user_role_info(current_user: CurrentUser) -> Message:
    """
    Get information about the user's role.
    Accessible by any authenticated user.
    """
    role_info = {
        "user": "Regular user with basic access",
        "coach": "Coach with ability to manage training sessions and view user data",
        "admin": "Administrator with full system access",
    }

    return Message(
        message=f"Your role is: {current_user.role}. {role_info.get(current_user.role, '')}"
    )
