import uuid
from typing import Any

from fastapi import APIRouter, HTTPException, Query
from sqlmodel import func, select

from app import crud
from app.api.deps import <PERSON><PERSON><PERSON><PERSON>, CoachUser, CurrentUser, SessionDep
from app.models import (
    CategoriesPublic,
    Category,
    CategoryPublic,
    CoachesPublic,
    CoachProfileCreate,
    CoachProfilePublic,
    CoachProfileUpdate,
    Location,
    LocationPublic,
    LocationsPublic,
    Review,
    ReviewCreate,
    ReviewPublic,
)

router = APIRouter()


# Category endpoints
@router.get("/categories", response_model=CategoriesPublic)
def read_categories(
    session: SessionDep, skip: int = 0, limit: int = Query(default=100, le=100)
) -> Any:
    """
    Retrieve categories for coach specializations.
    """
    categories = crud.get_categories(session=session, skip=skip, limit=limit)
    count_statement = select(func.count()).select_from(Category)
    count = session.exec(count_statement).one()

    return CategoriesPublic(data=categories, count=count)


@router.post("/categories", response_model=CategoryPublic)
def create_category(
    *,
    session: SessionDep,
    current_user: AdminUser,  # noqa: ARG001
    name: str,
    description: str | None = None,
) -> Any:
    """
    Create new category (admin only).
    """
    category = crud.create_category(session=session, name=name, description=description)
    return category


# Location endpoints
@router.get("/locations", response_model=LocationsPublic)
def read_locations(
    session: SessionDep, skip: int = 0, limit: int = Query(default=100, le=100)
) -> Any:
    """
    Retrieve locations where coaches are available.
    """
    locations = crud.get_locations(session=session, skip=skip, limit=limit)
    count_statement = select(func.count()).select_from(Location)
    count = session.exec(count_statement).one()

    return LocationsPublic(data=locations, count=count)


@router.post("/locations", response_model=LocationPublic)
def create_location(
    *,
    session: SessionDep,
    current_user: AdminUser,  # noqa: ARG001
    city: str,
    state: str,
    country: str,
) -> Any:
    """
    Create new location (admin only).
    """
    location = crud.create_location(
        session=session, city=city, state=state, country=country
    )
    return location


# Coach profile endpoints
@router.get("/coaches", response_model=CoachesPublic)
def browse_coaches(
    session: SessionDep,
    skip: int = 0,
    limit: int = Query(default=20, le=100),
    category_id: uuid.UUID | None = Query(
        default=None, description="Filter by category"
    ),
    location_id: uuid.UUID | None = Query(
        default=None, description="Filter by location"
    ),
    min_rating: float | None = Query(
        default=None, ge=0, le=5, description="Minimum rating"
    ),
    max_hourly_rate: float | None = Query(
        default=None, ge=0, description="Maximum hourly rate"
    ),
    search: str | None = Query(
        default=None, description="Search in coach name and bio"
    ),
) -> Any:
    """
    Browse spiritual coaches with filters.
    """
    coaches = crud.get_coaches_with_filters(
        session=session,
        skip=skip,
        limit=limit,
        category_id=category_id,
        location_id=location_id,
        min_rating=min_rating,
        max_hourly_rate=max_hourly_rate,
        search_query=search,
    )

    count = crud.count_coaches_with_filters(
        session=session,
        category_id=category_id,
        location_id=location_id,
        min_rating=min_rating,
        max_hourly_rate=max_hourly_rate,
        search_query=search,
    )

    return CoachesPublic(data=coaches, count=count)


@router.get("/coaches/{coach_id}", response_model=CoachProfilePublic)
def read_coach(*, session: SessionDep, coach_id: uuid.UUID) -> Any:
    """
    Get coach profile by ID.
    """
    coach = crud.get_coach_profile_by_id(session=session, profile_id=coach_id)
    if not coach:
        raise HTTPException(status_code=404, detail="Coach not found")
    return coach


@router.post("/coaches/profile", response_model=CoachProfilePublic)
def create_coach_profile(
    *,
    session: SessionDep,
    current_user: CoachUser,
    coach_profile_in: CoachProfileCreate,
) -> Any:
    """
    Create coach profile (coach or admin only).
    """
    # Check if user already has a coach profile
    existing_profile = crud.get_coach_profile_by_user_id(
        session=session, user_id=current_user.id
    )
    if existing_profile:
        raise HTTPException(status_code=400, detail="User already has a coach profile")

    # Validate category and location if provided
    if coach_profile_in.category_id:
        category = crud.get_category_by_id(
            session=session, category_id=coach_profile_in.category_id
        )
        if not category:
            raise HTTPException(status_code=404, detail="Category not found")

    if coach_profile_in.location_id:
        location = crud.get_location_by_id(
            session=session, location_id=coach_profile_in.location_id
        )
        if not location:
            raise HTTPException(status_code=404, detail="Location not found")

    coach_profile = crud.create_coach_profile(
        session=session, user_id=current_user.id, coach_profile_in=coach_profile_in
    )
    return coach_profile


@router.put("/coaches/profile", response_model=CoachProfilePublic)
def update_coach_profile(
    *,
    session: SessionDep,
    current_user: CoachUser,
    coach_profile_in: CoachProfileUpdate,
) -> Any:
    """
    Update coach profile (coach or admin only).
    """
    coach_profile = crud.get_coach_profile_by_user_id(
        session=session, user_id=current_user.id
    )
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")

    # Validate category and location if provided
    if coach_profile_in.category_id:
        category = crud.get_category_by_id(
            session=session, category_id=coach_profile_in.category_id
        )
        if not category:
            raise HTTPException(status_code=404, detail="Category not found")

    if coach_profile_in.location_id:
        location = crud.get_location_by_id(
            session=session, location_id=coach_profile_in.location_id
        )
        if not location:
            raise HTTPException(status_code=404, detail="Location not found")

    coach_profile = crud.update_coach_profile(
        session=session,
        db_coach_profile=coach_profile,
        coach_profile_in=coach_profile_in,
    )
    return coach_profile


@router.get("/coaches/profile/me", response_model=CoachProfilePublic)
def read_my_coach_profile(*, session: SessionDep, current_user: CoachUser) -> Any:
    """
    Get current user's coach profile.
    """
    coach_profile = crud.get_coach_profile_by_user_id(
        session=session, user_id=current_user.id
    )
    if not coach_profile:
        raise HTTPException(status_code=404, detail="Coach profile not found")
    return coach_profile


# Review endpoints
@router.post("/coaches/{coach_id}/reviews", response_model=ReviewPublic)
def create_review(
    *,
    session: SessionDep,
    current_user: CurrentUser,
    coach_id: uuid.UUID,
    review_in: ReviewCreate,
) -> Any:
    """
    Create a review for a coach.
    """
    # Verify coach exists
    coach = crud.get_coach_profile_by_id(session=session, profile_id=coach_id)
    if not coach:
        raise HTTPException(status_code=404, detail="Coach not found")

    # Prevent self-review
    if coach.user_id == current_user.id:
        raise HTTPException(status_code=400, detail="Cannot review yourself")

    # Check if user already reviewed this coach
    existing_review = session.exec(
        select(Review).where(
            Review.coach_profile_id == coach_id, Review.user_id == current_user.id
        )
    ).first()

    if existing_review:
        raise HTTPException(
            status_code=400, detail="You have already reviewed this coach"
        )

    # Set the coach_profile_id from the URL parameter
    review_in.coach_profile_id = coach_id

    review = crud.create_review(
        session=session, user_id=current_user.id, review_in=review_in
    )
    return review


@router.get("/coaches/{coach_id}/reviews", response_model=list[ReviewPublic])
def read_coach_reviews(
    *,
    session: SessionDep,
    coach_id: uuid.UUID,
    skip: int = 0,
    limit: int = Query(default=20, le=100),
) -> Any:
    """
    Get reviews for a specific coach.
    """
    # Verify coach exists
    coach = crud.get_coach_profile_by_id(session=session, profile_id=coach_id)
    if not coach:
        raise HTTPException(status_code=404, detail="Coach not found")

    reviews = crud.get_reviews_for_coach(
        session=session, coach_profile_id=coach_id, skip=skip, limit=limit
    )
    return reviews
