import uuid
from datetime import datetime
from typing import Optional

from pydantic import Field, validator
from sqlmodel import SQLModel

from app.models.event import EventStatus, EventType, RSVPStatus
from app.schemas.coach import CoachProfilePublic
from app.schemas.common import PaginatedResponse
from app.schemas.location import LocationPublic
from app.schemas.user import UserPublic


# Base event schemas
class EventBase(SQLModel):
    title: str = Field(max_length=200, description="Event title")
    description: str = Field(description="Detailed event description")
    event_date: datetime = Field(description="Event date and time")
    duration: int = Field(ge=30, le=480, default=90, description="Event duration in minutes")
    capacity: int = Field(ge=1, le=1000, default=20, description="Maximum number of participants")
    price: float = Field(ge=0, default=0.0, description="Event price per person")
    event_type: EventType = Field(default=EventType.ONLINE, description="Event type")
    category: Optional[str] = Field(default=None, max_length=100, description="Event category")
    tags: Optional[str] = Field(default=None, max_length=500, description="Event tags")


class EventCreate(EventBase):
    # Location details (for in-person events)
    location_id: Optional[uuid.UUID] = Field(default=None, description="Location ID for in-person events")
    address: Optional[str] = Field(default=None, max_length=500, description="Specific address")
    
    # Online event details
    meeting_link: Optional[str] = Field(default=None, max_length=500, description="Meeting link")
    meeting_password: Optional[str] = Field(default=None, max_length=100, description="Meeting password")
    
    # Registration settings
    registration_deadline: Optional[datetime] = Field(default=None, description="Registration deadline")
    allow_waitlist: bool = Field(default=True, description="Allow waitlist when full")
    require_approval: bool = Field(default=False, description="Require host approval")
    
    # Additional information
    what_to_bring: Optional[str] = Field(default=None, max_length=1000, description="What to bring")
    prerequisites: Optional[str] = Field(default=None, max_length=1000, description="Prerequisites")
    
    @validator('event_date')
    def event_date_must_be_future(cls, v):
        if v and v <= datetime.utcnow():
            raise ValueError('Event date must be in the future')
        return v
    
    @validator('registration_deadline')
    def registration_deadline_before_event(cls, v, values):
        if v and 'event_date' in values and v >= values['event_date']:
            raise ValueError('Registration deadline must be before event date')
        return v


class EventUpdate(SQLModel):
    title: Optional[str] = Field(default=None, max_length=200)
    description: Optional[str] = Field(default=None)
    event_date: Optional[datetime] = Field(default=None)
    duration: Optional[int] = Field(default=None, ge=30, le=480)
    capacity: Optional[int] = Field(default=None, ge=1, le=1000)
    price: Optional[float] = Field(default=None, ge=0)
    event_type: Optional[EventType] = Field(default=None)
    location_id: Optional[uuid.UUID] = Field(default=None)
    address: Optional[str] = Field(default=None, max_length=500)
    meeting_link: Optional[str] = Field(default=None, max_length=500)
    meeting_password: Optional[str] = Field(default=None, max_length=100)
    status: Optional[EventStatus] = Field(default=None)
    registration_deadline: Optional[datetime] = Field(default=None)
    allow_waitlist: Optional[bool] = Field(default=None)
    require_approval: Optional[bool] = Field(default=None)
    category: Optional[str] = Field(default=None, max_length=100)
    tags: Optional[str] = Field(default=None, max_length=500)
    what_to_bring: Optional[str] = Field(default=None, max_length=1000)
    prerequisites: Optional[str] = Field(default=None, max_length=1000)


class EventPublic(EventBase):
    id: uuid.UUID
    host_id: uuid.UUID
    current_attendees: int
    status: EventStatus
    location_id: Optional[uuid.UUID]
    address: Optional[str]
    meeting_link: Optional[str]  # Only shown to registered participants
    meeting_password: Optional[str]  # Only shown to registered participants
    registration_deadline: Optional[datetime]
    allow_waitlist: bool
    require_approval: bool
    what_to_bring: Optional[str]
    prerequisites: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    # Related data
    host: CoachProfilePublic
    location: Optional[LocationPublic]
    
    # Computed fields
    is_full: bool = Field(default=False, description="Whether event is at capacity")
    spots_remaining: int = Field(default=0, description="Number of spots remaining")
    is_free: bool = Field(default=True, description="Whether event is free")


class EventPublicSimple(SQLModel):
    """Simplified event info for lists"""
    id: uuid.UUID
    title: str
    event_date: datetime
    duration: int
    capacity: int
    current_attendees: int
    price: float
    event_type: EventType
    status: EventStatus
    category: Optional[str]
    host: CoachProfilePublic
    is_full: bool
    spots_remaining: int


class EventsPublic(PaginatedResponse):
    data: list[EventPublic]


class EventsPublicSimple(PaginatedResponse):
    data: list[EventPublicSimple]


# RSVP schemas
class RSVPBase(SQLModel):
    registration_notes: Optional[str] = Field(default=None, max_length=1000, description="Registration notes")
    dietary_restrictions: Optional[str] = Field(default=None, max_length=500, description="Dietary restrictions")


class RSVPCreate(RSVPBase):
    event_id: uuid.UUID = Field(description="Event ID to RSVP for")


class RSVPUpdate(SQLModel):
    status: Optional[RSVPStatus] = Field(default=None)
    registration_notes: Optional[str] = Field(default=None, max_length=1000)
    dietary_restrictions: Optional[str] = Field(default=None, max_length=500)


class RSVPPublic(RSVPBase):
    id: uuid.UUID
    event_id: uuid.UUID
    user_id: uuid.UUID
    status: RSVPStatus
    payment_required: bool
    payment_completed: bool
    amount_paid: float
    is_waitlisted: bool
    waitlist_position: Optional[int]
    checked_in: bool
    check_in_time: Optional[datetime]
    created_at: datetime
    
    # Related data
    event: EventPublicSimple
    user: UserPublic


class RSVPPublicSimple(SQLModel):
    """Simplified RSVP info"""
    id: uuid.UUID
    status: RSVPStatus
    payment_completed: bool
    is_waitlisted: bool
    checked_in: bool
    user: UserPublic


class RSVPsPublic(PaginatedResponse):
    data: list[RSVPPublic]


# Event payment schemas
class EventPaymentCreate(SQLModel):
    rsvp_id: uuid.UUID = Field(description="RSVP ID for payment")


class EventPaymentPublic(SQLModel):
    id: uuid.UUID
    rsvp_id: uuid.UUID
    user_id: uuid.UUID
    event_id: uuid.UUID
    amount: float
    currency: str
    status: str
    description: Optional[str]
    created_at: datetime
    paid_at: Optional[datetime]


# Event search and filtering
class EventSearchFilters(SQLModel):
    event_type: Optional[EventType] = Field(default=None, description="Filter by event type")
    category: Optional[str] = Field(default=None, description="Filter by category")
    location_id: Optional[uuid.UUID] = Field(default=None, description="Filter by location")
    host_id: Optional[uuid.UUID] = Field(default=None, description="Filter by host")
    min_price: Optional[float] = Field(default=None, ge=0, description="Minimum price")
    max_price: Optional[float] = Field(default=None, ge=0, description="Maximum price")
    start_date: Optional[datetime] = Field(default=None, description="Events after this date")
    end_date: Optional[datetime] = Field(default=None, description="Events before this date")
    has_spots_available: Optional[bool] = Field(default=None, description="Only events with spots")
    is_free: Optional[bool] = Field(default=None, description="Only free events")
    tags: Optional[str] = Field(default=None, description="Search by tags")


# Stripe checkout for events
class EventStripeCheckoutRequest(SQLModel):
    rsvp_id: uuid.UUID = Field(description="RSVP ID for payment")
    success_url: Optional[str] = Field(default=None, description="Success redirect URL")
    cancel_url: Optional[str] = Field(default=None, description="Cancel redirect URL")


class EventStripeCheckoutResponse(SQLModel):
    checkout_url: str = Field(description="Stripe checkout URL")
    session_id: str = Field(description="Stripe session ID")
    payment_id: uuid.UUID = Field(description="Event payment ID")


# Event statistics
class EventStats(SQLModel):
    total_events: int = 0
    published_events: int = 0
    completed_events: int = 0
    total_attendees: int = 0
    total_revenue: float = 0.0
    average_attendance_rate: float = 0.0


class HostEventStats(SQLModel):
    total_events_hosted: int = 0
    total_attendees: int = 0
    total_revenue: float = 0.0
    average_rating: Optional[float] = None
    upcoming_events: int = 0
