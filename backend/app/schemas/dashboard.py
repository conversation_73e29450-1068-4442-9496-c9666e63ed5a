import uuid
from datetime import datetime
from typing import Optional

from pydantic import Field
from sqlmodel import SQLModel

from app.models.booking import BookingStatus
from app.models.payment import PaymentStatus
from app.schemas.booking import BookingPublicSimple
from app.schemas.coach import CoachProfilePublic
from app.schemas.payment import PaymentPublic
from app.schemas.review import ReviewPublic
from app.schemas.user import UserPublic


# Dashboard-specific simplified schemas
class DashboardBooking(SQLModel):
    """Simplified booking info for dashboard"""
    id: uuid.UUID
    session_time: datetime
    duration: int
    status: BookingStatus
    session_type: Optional[str]
    notes: Optional[str]
    meeting_link: Optional[str]
    created_at: datetime


class DashboardBookingWithCoach(DashboardBooking):
    """Dashboard booking with coach info for users"""
    coach: CoachProfilePublic


class DashboardBookingWithUser(DashboardBooking):
    """Dashboard booking with user info for coaches"""
    user: UserPublic


class DashboardPayment(SQLModel):
    """Simplified payment info for dashboard"""
    id: uuid.UUID
    amount: float
    status: PaymentStatus
    payment_method: str
    created_at: datetime
    paid_at: Optional[datetime]


class DashboardReview(SQLModel):
    """Simplified review info for dashboard"""
    id: uuid.UUID
    rating: int
    comment: Optional[str]
    is_featured: bool
    session_type: Optional[str]
    created_at: datetime


class DashboardReviewWithCoach(DashboardReview):
    """Dashboard review with coach info for users"""
    coach: CoachProfilePublic


class DashboardStats(SQLModel):
    """General statistics for dashboard"""
    total_bookings: int = 0
    confirmed_bookings: int = 0
    completed_bookings: int = 0
    cancelled_bookings: int = 0
    total_spent: float = 0.0
    total_reviews: int = 0
    average_rating_given: Optional[float] = None


class CoachDashboardStats(SQLModel):
    """Coach-specific statistics"""
    total_sessions: int = 0
    confirmed_sessions: int = 0
    completed_sessions: int = 0
    cancelled_sessions: int = 0
    total_earnings: float = 0.0
    total_reviews_received: int = 0
    average_rating_received: Optional[float] = None
    featured_testimonials: int = 0
    this_month_sessions: int = 0
    this_month_earnings: float = 0.0


# Main dashboard response schemas
class UserDashboard(SQLModel):
    """Complete user dashboard data"""
    # User info
    user: Optional[UserPublic] = None

    # Upcoming sessions (next 30 days)
    upcoming_sessions: list[DashboardBookingWithCoach] = Field(default_factory=list)

    # Recent bookings (last 30 days)
    recent_bookings: list[DashboardBookingWithCoach] = Field(default_factory=list)

    # Reviews left by user
    reviews_left: list[DashboardReviewWithCoach] = Field(default_factory=list)

    # Recent payments
    recent_payments: list[DashboardPayment] = Field(default_factory=list)

    # Statistics
    stats: DashboardStats

    # Quick actions data
    favorite_coaches: list[CoachProfilePublic] = Field(default_factory=list)
    pending_payments: list[DashboardPayment] = Field(default_factory=list)


class CoachDashboard(SQLModel):
    """Complete coach dashboard data"""
    # Coach info
    user: Optional[UserPublic] = None
    coach_profile: Optional[CoachProfilePublic] = None

    # Upcoming sessions (next 30 days)
    upcoming_sessions: list[DashboardBookingWithUser] = Field(default_factory=list)

    # Recent sessions (last 30 days)
    recent_sessions: list[DashboardBookingWithUser] = Field(default_factory=list)

    # Reviews received
    reviews_received: list[DashboardReview] = Field(default_factory=list)

    # Recent earnings
    recent_earnings: list[DashboardPayment] = Field(default_factory=list)

    # Statistics
    stats: CoachDashboardStats

    # Quick actions data
    pending_sessions: list[DashboardBookingWithUser] = Field(default_factory=list)
    featured_testimonials: list[DashboardReview] = Field(default_factory=list)


class AdminDashboard(SQLModel):
    """Admin dashboard with platform-wide statistics"""
    # Platform statistics
    total_users: int = 0
    total_coaches: int = 0
    total_bookings: int = 0
    total_payments: float = 0.0
    total_reviews: int = 0

    # Recent activity
    recent_bookings: list[BookingPublicSimple] = Field(default_factory=list)
    recent_payments: list[PaymentPublic] = Field(default_factory=list)
    recent_reviews: list[ReviewPublic] = Field(default_factory=list)

    # This month statistics
    this_month_bookings: int = 0
    this_month_revenue: float = 0.0
    this_month_new_users: int = 0
    this_month_new_coaches: int = 0


# Unified dashboard response that adapts based on user role
class DashboardResponse(SQLModel):
    """Unified dashboard response that contains role-specific data"""
    user_role: str = Field(description="Role of the current user")

    # Role-specific dashboard data (only one will be populated)
    user_dashboard: Optional[UserDashboard] = None
    coach_dashboard: Optional[CoachDashboard] = None
    admin_dashboard: Optional[AdminDashboard] = None
