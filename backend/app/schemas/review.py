import uuid
from datetime import datetime

from pydantic import Field
from sqlmodel import SQLModel

from app.schemas.user import UserPublic


class ReviewPublic(SQLModel):
    id: uuid.UUID
    rating: int
    comment: str | None
    created_at: datetime
    user: UserPublic


class ReviewCreate(SQLModel):
    coach_profile_id: uuid.UUID
    rating: int = Field(ge=1, le=5)
    comment: str | None = None
