# Import all schemas
from app.schemas.booking import (
    BookingCreate,
    BookingPublic,
    BookingPublicSimple,
    BookingPublicWithCoach,
    BookingPublicWithUser,
    BookingsPublic,
    BookingsPublicSimple,
    BookingsPublicWithCoach,
    BookingsPublicWithUser,
    BookingStatusUpdate,
    BookingUpdate,
)
from app.schemas.coach import (
    CategoriesPublic,
    CategoryPublic,
    CoachesPublic,
    CoachProfileCreate,
    CoachProfilePublic,
    CoachProfileUpdate,
    CoachSearchFilters,
    LocationPublic,
    LocationsPublic,
)
from app.schemas.coach_availability import (
    AvailableTimeSlot,
    AvailableTimeSlotsResponse,
    BulkAvailabilityCreate,
    BulkAvailabilityResponse,
    CoachAvailabilitiesPublic,
    CoachAvailabilityCreate,
    CoachAvailabilityPublic,
    CoachAvailabilityUpdate,
    TimeSlotRequest,
    WeeklyAvailabilityRequest,
    WeeklyAvailabilityResponse,
)
from app.schemas.dashboard import (
    AdminDashboard,
    CoachDashboard,
    CoachDashboardStats,
    DashboardBookingWithCoach,
    DashboardBookingWithUser,
    DashboardPayment,
    DashboardResponse,
    DashboardReview,
    DashboardReviewWithCoach,
    DashboardStats,
    UserDashboard,
)
from app.schemas.event import (
    EventCreate,
    EventPublic,
    EventPublicSimple,
    EventsPublic,
    EventsPublicSimple,
    EventSearchFilters,
    EventStats,
    EventStripeCheckoutRequest,
    EventStripeCheckoutResponse,
    EventUpdate,
    HostEventStats,
    RSVPCreate,
    RSVPPublic,
    RSVPPublicSimple,
    RSVPsPublic,
    RSVPUpdate,
)
from app.schemas.common import Message, NewPassword, Token, TokenPayload
from app.schemas.item import ItemCreate, ItemPublic, ItemsPublic, ItemUpdate
from app.schemas.payment import (
    PaymentCreate,
    PaymentPublic,
    PaymentsPublic,
    PaymentRefundRequest,
    PaymentRefundResponse,
    PaymentSummary,
    PaymentUpdate,
    StripeCheckoutRequest,
    StripeCheckoutResponse,
    StripeWebhookEventPublic,
    StripeWebhookEventsPublic,
    StripeWebhookPayload,
)
from app.schemas.review import (
    FeaturedTestimonialsResponse,
    ReviewCreate,
    ReviewPublic,
    ReviewsPublic,
    ReviewUpdate,
    TestimonialPublic,
    TestimonialsPublic,
)
from app.schemas.user import (
    UpdatePassword,
    UserCreate,
    UserPublic,
    UserRegister,
    UsersPublic,
    UserUpdate,
    UserUpdateMe,
)

__all__ = [
    # Common
    "Message",
    "Token",
    "TokenPayload",
    "NewPassword",
    # User
    "UserCreate",
    "UserRegister",
    "UserUpdate",
    "UserUpdateMe",
    "UpdatePassword",
    "UserPublic",
    "UsersPublic",
    # Item
    "ItemCreate",
    "ItemUpdate",
    "ItemPublic",
    "ItemsPublic",
    # Coach
    "CategoryPublic",
    "LocationPublic",
    "CoachProfilePublic",
    "CoachProfileCreate",
    "CoachProfileUpdate",
    "CoachSearchFilters",
    "CoachesPublic",
    "CategoriesPublic",
    "LocationsPublic",
    # Review
    "ReviewPublic",
    "ReviewCreate",
    # Booking
    "BookingCreate",
    "BookingUpdate",
    "BookingPublic",
    "BookingPublicSimple",
    "BookingPublicWithUser",
    "BookingPublicWithCoach",
    "BookingsPublic",
    "BookingsPublicSimple",
    "BookingsPublicWithUser",
    "BookingsPublicWithCoach",
    "BookingStatusUpdate",
    # Coach Availability
    "CoachAvailabilityCreate",
    "CoachAvailabilityUpdate",
    "CoachAvailabilityPublic",
    "CoachAvailabilitiesPublic",
    "AvailableTimeSlot",
    "AvailableTimeSlotsResponse",
    "WeeklyAvailabilityRequest",
    "WeeklyAvailabilityResponse",
    "TimeSlotRequest",
    "BulkAvailabilityCreate",
    "BulkAvailabilityResponse",
    # Payment
    "PaymentCreate",
    "PaymentUpdate",
    "PaymentPublic",
    "PaymentsPublic",
    "PaymentSummary",
    "PaymentRefundRequest",
    "PaymentRefundResponse",
    "StripeCheckoutRequest",
    "StripeCheckoutResponse",
    "StripeWebhookPayload",
    "StripeWebhookEventPublic",
    "StripeWebhookEventsPublic",
    # Review & Testimonials
    "ReviewCreate",
    "ReviewUpdate",
    "ReviewPublic",
    "ReviewsPublic",
    "TestimonialPublic",
    "TestimonialsPublic",
    "FeaturedTestimonialsResponse",
    # Dashboard
    "DashboardResponse",
    "UserDashboard",
    "CoachDashboard",
    "AdminDashboard",
    "DashboardStats",
    "CoachDashboardStats",
    "DashboardBookingWithCoach",
    "DashboardBookingWithUser",
    "DashboardPayment",
    "DashboardReview",
    "DashboardReviewWithCoach",
    # Events
    "EventCreate",
    "EventUpdate",
    "EventPublic",
    "EventPublicSimple",
    "EventsPublic",
    "EventsPublicSimple",
    "EventSearchFilters",
    "EventStats",
    "HostEventStats",
    "EventStripeCheckoutRequest",
    "EventStripeCheckoutResponse",
    "RSVPCreate",
    "RSVPUpdate",
    "RSVPPublic",
    "RSVPPublicSimple",
    "RSVPsPublic",
]
