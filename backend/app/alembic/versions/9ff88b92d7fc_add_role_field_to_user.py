"""add_role_field_to_user

Revision ID: 9ff88b92d7fc
Revises: 1a31ce608336
Create Date: 2025-05-22 15:09:51.581881

"""
from alembic import op
import sqlalchemy as sa
import sqlmodel.sql.sqltypes


# revision identifiers, used by Alembic.
revision = '9ff88b92d7fc'
down_revision = '1a31ce608336'
branch_labels = None
depends_on = None


def upgrade():
    # Create a temporary column
    op.add_column('user', sa.Column('role_new', sa.String(50), nullable=True))
    
    # Copy data from old column to new column
    op.execute("UPDATE \"user\" SET role_new = role::text")
    
    # Drop the old column
    op.drop_column('user', 'role')
    
    # Rename the new column to the original name
    op.alter_column('user', 'role_new', new_column_name='role')
    
    # Drop the enum type
    op.execute("DROP TYPE IF EXISTS userrole")


def downgrade():
    # Create the enum type
    op.execute("CREATE TYPE userrole AS ENUM ('user', 'coach', 'admin')")
    
    # Create a temporary column
    op.add_column('user', sa.Column('role_new', sa.Enum('user', 'coach', 'admin', name='userrole'), nullable=True))
    
    # Copy data from string column to enum column
    op.execute("UPDATE \"user\" SET role_new = role::userrole")
    
    # Drop the old column
    op.drop_column('user', 'role')
    
    # Rename the new column to the original name
    op.alter_column('user', 'role_new', new_column_name='role')

    # ### end Alembic commands ###
