import uuid
from datetime import datetime

from sqlmodel import Column, Field, Relationship, SQLModel, Text


# Review model for coach ratings and feedback
class Review(SQLModel, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)

    # Foreign keys
    coach_profile_id: uuid.UUID = Field(foreign_key="coachprofile.id", nullable=False)
    user_id: uuid.UUID = Field(foreign_key="user.id", nullable=False)

    # Review content
    rating: int = Field(ge=1, le=5)
    comment: str | None = Field(default=None, sa_column=Column(Text))

    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    coach_profile: "CoachProfile" = Relationship(back_populates="reviews")
    user: "User" = Relationship()
