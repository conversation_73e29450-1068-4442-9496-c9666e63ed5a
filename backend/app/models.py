import uuid
import time 
from pydantic import EmailStr
from sqlmodel import Field, Relationship, SQLModel
from datetime import datetime
from sqlalchemy.dialects.postgresql import CITEXT

# Shared properties
class UserBase(SQLModel):
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)


# Properties to receive via API on creation
class UserCreate(UserBase):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40, nullable=False)
    country: str = Field( max_length=40, nullable=False)
    lastname: str | None = Field(nullable=False, max_length=255)
    firstname : str | None = Field(nullable=False, max_length=255)
    type : int = Field(nullable=False) 
    
class UserRegister(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40, nullable=False)
    country: str = Field( max_length=40, nullable=False)
    lastname: str | None = Field(nullable=False, max_length=255)
    firstname : str | None = Field(nullable=False, max_length=255)
    type : int = Field(nullable=False) 

class UserLogin(SQLModel):
    email: EmailStr = Field(max_length=255)
    password: str = Field(min_length=8, max_length=40, nullable=False) 
    type : int = Field(nullable=False) 
    
# Properties to receive via API on update, all are optional
class UserUpdate(UserBase):
    email: EmailStr | None = Field(default=None, max_length=255)  # type: ignore
    password: str | None = Field(default=None, min_length=8, max_length=40)


class UserUpdateMe(SQLModel):
    full_name: str | None = Field(default=None, max_length=255)
    email: EmailStr | None = Field(default=None, max_length=255)


class UpdatePassword(SQLModel):
    current_password: str = Field(min_length=8, max_length=40)
    new_password: str = Field(min_length=8, max_length=40)


class User(SQLModel, table=True):
    __tablename__ = 'users'  # Custom table name
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True, alias="id")  # user_id (SERIAL)
    firstname: str = Field(max_length=255, nullable=False)  # firstname
    lastname: str = Field(max_length=255, nullable=False)  # lastname
    type: int = Field(nullable=False) 
    country: str = Field(max_length=255, nullable=False) 
    email: str = Field(max_length=255, nullable=False, unique=True)  # email (TEXT), case-insensitive (CITEXT)
    password: str = Field(max_length=255, nullable=False)  # password (VARCHAR(255))

    created_at: int = Field(nullable=False, default=int(time.time()))   # created_at (TIMESTAMPTZ)
    updated_at: int = Field(nullable=False, default=int(time.time()))  # updated_at (TIMESTAMPTZ)

    @classmethod
    def update_timestamp(cls, instance):
        instance.updated_at = int(time.time())
        return instance



# Properties to return via API, id is always required
class UserPublic(UserBase):
    id: uuid.UUID


class UsersPublic(SQLModel):
    data: list[UserPublic]
    count: int


# Shared properties
class ItemBase(SQLModel):
    title: str = Field(min_length=1, max_length=255)
    description: str | None = Field(default=None, max_length=255)


# Properties to receive on item creation
class ItemCreate(ItemBase):
    pass


# Properties to receive on item update
class ItemUpdate(ItemBase):
    title: str | None = Field(default=None, min_length=1, max_length=255)  # type: ignore


# Database model, database table inferred from class name
class Item(ItemBase, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    title: str = Field(max_length=255)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    # owner: User | None = Relationship(back_populates="items")


# Properties to return via API, id is always required
class ItemPublic(ItemBase):
    id: uuid.UUID
    owner_id: uuid.UUID


class ItemsPublic(SQLModel):
    data: list[ItemPublic]
    count: int


# Generic message
class Message(SQLModel):
    message: str


# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str = Field(min_length=8, max_length=40)
