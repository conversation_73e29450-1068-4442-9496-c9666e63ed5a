"""
CRUD operations for spiritual coaches, categories, locations, and reviews.
"""

import uuid

from sqlmodel import Session, func, or_, select

from app.models import (
    Category,
    CoachProfile,
    CoachProfileCreate,
    CoachProfileUpdate,
    Location,
    Review,
    ReviewCreate,
    User,
)


# Category CRUD operations
def create_category(
    *, session: Session, name: str, description: str | None = None
) -> Category:
    """Create a new category."""
    db_obj = Category(name=name, description=description)
    session.add(db_obj)
    session.commit()
    session.refresh(db_obj)
    return db_obj


def get_categories(
    *, session: Session, skip: int = 0, limit: int = 100
) -> list[Category]:
    """Get all categories."""
    statement = select(Category).offset(skip).limit(limit)
    return session.exec(statement).all()


def get_category_by_id(*, session: Session, category_id: uuid.UUID) -> Category | None:
    """Get category by ID."""
    statement = select(Category).where(Category.id == category_id)
    return session.exec(statement).first()


# Location CRUD operations
def create_location(
    *, session: Session, city: str, state: str, country: str
) -> Location:
    """Create a new location."""
    db_obj = Location(city=city, state=state, country=country)
    session.add(db_obj)
    session.commit()
    session.refresh(db_obj)
    return db_obj


def get_locations(
    *, session: Session, skip: int = 0, limit: int = 100
) -> list[Location]:
    """Get all locations."""
    statement = select(Location).offset(skip).limit(limit)
    return session.exec(statement).all()


def get_location_by_id(*, session: Session, location_id: uuid.UUID) -> Location | None:
    """Get location by ID."""
    statement = select(Location).where(Location.id == location_id)
    return session.exec(statement).first()


# Coach Profile CRUD operations
def create_coach_profile(
    *, session: Session, user_id: uuid.UUID, coach_profile_in: CoachProfileCreate
) -> CoachProfile:
    """Create a new coach profile."""
    db_obj = CoachProfile(user_id=user_id, **coach_profile_in.model_dump())
    session.add(db_obj)
    session.commit()
    session.refresh(db_obj)
    return db_obj


def get_coach_profile_by_user_id(
    *, session: Session, user_id: uuid.UUID
) -> CoachProfile | None:
    """Get coach profile by user ID."""
    statement = select(CoachProfile).where(CoachProfile.user_id == user_id)
    return session.exec(statement).first()


def get_coach_profile_by_id(
    *, session: Session, profile_id: uuid.UUID
) -> CoachProfile | None:
    """Get coach profile by ID."""
    statement = select(CoachProfile).where(CoachProfile.id == profile_id)
    return session.exec(statement).first()


def update_coach_profile(
    *,
    session: Session,
    db_coach_profile: CoachProfile,
    coach_profile_in: CoachProfileUpdate,
) -> CoachProfile:
    """Update coach profile."""
    coach_profile_data = coach_profile_in.model_dump(exclude_unset=True)
    db_coach_profile.sqlmodel_update(coach_profile_data)
    session.add(db_coach_profile)
    session.commit()
    session.refresh(db_coach_profile)
    return db_coach_profile


def get_coaches_with_filters(
    *,
    session: Session,
    skip: int = 0,
    limit: int = 100,
    category_id: uuid.UUID | None = None,
    location_id: uuid.UUID | None = None,
    min_rating: float | None = None,
    max_hourly_rate: float | None = None,
    is_available: bool | None = None,
    search_query: str | None = None,
) -> list[CoachProfile]:
    """Get coaches with various filters."""
    statement = (
        select(CoachProfile)
        .join(User, CoachProfile.user_id == User.id)
        .where(
            CoachProfile.is_available == True
        )  # Only show available coaches by default
    )

    # Apply filters
    if category_id:
        statement = statement.where(CoachProfile.category_id == category_id)

    if location_id:
        statement = statement.where(CoachProfile.location_id == location_id)

    if min_rating is not None:
        statement = statement.where(CoachProfile.average_rating >= min_rating)

    if max_hourly_rate is not None:
        statement = statement.where(CoachProfile.hourly_rate <= max_hourly_rate)

    if is_available is not None:
        statement = statement.where(CoachProfile.is_available == is_available)

    if search_query:
        # Search in user's full name and coach bio
        search_filter = or_(
            User.full_name.ilike(f"%{search_query}%"),
            CoachProfile.bio.ilike(f"%{search_query}%"),
        )
        statement = statement.where(search_filter)

    statement = statement.offset(skip).limit(limit)
    return session.exec(statement).all()


def count_coaches_with_filters(
    *,
    session: Session,
    category_id: uuid.UUID | None = None,
    location_id: uuid.UUID | None = None,
    min_rating: float | None = None,
    max_hourly_rate: float | None = None,
    is_available: bool | None = None,
    search_query: str | None = None,
) -> int:
    """Count coaches with filters."""
    statement = (
        select(func.count(CoachProfile.id))
        .join(User, CoachProfile.user_id == User.id)
        .where(CoachProfile.is_available == True)
    )

    # Apply same filters as get_coaches_with_filters
    if category_id:
        statement = statement.where(CoachProfile.category_id == category_id)

    if location_id:
        statement = statement.where(CoachProfile.location_id == location_id)

    if min_rating is not None:
        statement = statement.where(CoachProfile.average_rating >= min_rating)

    if max_hourly_rate is not None:
        statement = statement.where(CoachProfile.hourly_rate <= max_hourly_rate)

    if is_available is not None:
        statement = statement.where(CoachProfile.is_available == is_available)

    if search_query:
        search_filter = or_(
            User.full_name.ilike(f"%{search_query}%"),
            CoachProfile.bio.ilike(f"%{search_query}%"),
        )
        statement = statement.where(search_filter)

    return session.exec(statement).one()


# Review CRUD operations
def create_review(
    *, session: Session, user_id: uuid.UUID, review_in: ReviewCreate
) -> Review:
    """Create a new review."""
    db_obj = Review(user_id=user_id, **review_in.model_dump())
    session.add(db_obj)
    session.commit()

    # Update coach profile's average rating and total reviews
    update_coach_rating(session=session, coach_profile_id=review_in.coach_profile_id)

    session.refresh(db_obj)
    return db_obj


def get_reviews_for_coach(
    *, session: Session, coach_profile_id: uuid.UUID, skip: int = 0, limit: int = 100
) -> list[Review]:
    """Get reviews for a specific coach."""
    statement = (
        select(Review)
        .where(Review.coach_profile_id == coach_profile_id)
        .offset(skip)
        .limit(limit)
        .order_by(Review.created_at.desc())
    )
    return session.exec(statement).all()


def update_coach_rating(*, session: Session, coach_profile_id: uuid.UUID) -> None:
    """Update coach's average rating and total reviews count."""
    # Calculate average rating and count
    rating_stats = session.exec(
        select(func.avg(Review.rating), func.count(Review.id)).where(
            Review.coach_profile_id == coach_profile_id
        )
    ).first()

    if rating_stats and rating_stats[1] > 0:  # If there are reviews
        avg_rating, total_reviews = rating_stats

        # Update coach profile
        coach_profile = session.exec(
            select(CoachProfile).where(CoachProfile.id == coach_profile_id)
        ).first()

        if coach_profile:
            coach_profile.average_rating = round(avg_rating, 2) if avg_rating else None
            coach_profile.total_reviews = total_reviews
            session.add(coach_profile)
            session.commit()
