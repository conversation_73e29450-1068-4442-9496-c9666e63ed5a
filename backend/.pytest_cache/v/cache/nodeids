["app/tests/api/routes/test_items.py::test_create_item", "app/tests/api/routes/test_items.py::test_delete_item", "app/tests/api/routes/test_items.py::test_delete_item_not_enough_permissions", "app/tests/api/routes/test_items.py::test_delete_item_not_found", "app/tests/api/routes/test_items.py::test_read_item", "app/tests/api/routes/test_items.py::test_read_item_not_enough_permissions", "app/tests/api/routes/test_items.py::test_read_item_not_found", "app/tests/api/routes/test_items.py::test_read_items", "app/tests/api/routes/test_items.py::test_update_item", "app/tests/api/routes/test_items.py::test_update_item_not_enough_permissions", "app/tests/api/routes/test_items.py::test_update_item_not_found", "app/tests/api/routes/test_login.py::test_get_access_token", "app/tests/api/routes/test_login.py::test_get_access_token_incorrect_password", "app/tests/api/routes/test_login.py::test_recovery_password", "app/tests/api/routes/test_login.py::test_recovery_password_user_not_exits", "app/tests/api/routes/test_login.py::test_reset_password", "app/tests/api/routes/test_login.py::test_reset_password_invalid_token", "app/tests/api/routes/test_login.py::test_use_access_token", "app/tests/api/routes/test_private.py::test_create_user", "app/tests/api/routes/test_users.py::test_create_user_by_normal_user", "app/tests/api/routes/test_users.py::test_create_user_existing_username", "app/tests/api/routes/test_users.py::test_create_user_new_email", "app/tests/api/routes/test_users.py::test_delete_user_current_super_user_error", "app/tests/api/routes/test_users.py::test_delete_user_me", "app/tests/api/routes/test_users.py::test_delete_user_me_as_superuser", "app/tests/api/routes/test_users.py::test_delete_user_not_found", "app/tests/api/routes/test_users.py::test_delete_user_super_user", "app/tests/api/routes/test_users.py::test_delete_user_without_privileges", "app/tests/api/routes/test_users.py::test_get_existing_user", "app/tests/api/routes/test_users.py::test_get_existing_user_current_user", "app/tests/api/routes/test_users.py::test_get_existing_user_permissions_error", "app/tests/api/routes/test_users.py::test_get_users_normal_user_me", "app/tests/api/routes/test_users.py::test_get_users_superuser_me", "app/tests/api/routes/test_users.py::test_register_user", "app/tests/api/routes/test_users.py::test_register_user_already_exists_error", "app/tests/api/routes/test_users.py::test_retrieve_users", "app/tests/api/routes/test_users.py::test_update_password_me", "app/tests/api/routes/test_users.py::test_update_password_me_incorrect_password", "app/tests/api/routes/test_users.py::test_update_password_me_same_password_error", "app/tests/api/routes/test_users.py::test_update_user", "app/tests/api/routes/test_users.py::test_update_user_email_exists", "app/tests/api/routes/test_users.py::test_update_user_me", "app/tests/api/routes/test_users.py::test_update_user_me_email_exists", "app/tests/api/routes/test_users.py::test_update_user_not_exists", "app/tests/crud/test_user.py::test_authenticate_user", "app/tests/crud/test_user.py::test_check_if_user_is_active", "app/tests/crud/test_user.py::test_check_if_user_is_active_inactive", "app/tests/crud/test_user.py::test_check_if_user_is_superuser", "app/tests/crud/test_user.py::test_check_if_user_is_superuser_normal_user", "app/tests/crud/test_user.py::test_create_user", "app/tests/crud/test_user.py::test_get_user", "app/tests/crud/test_user.py::test_not_authenticate_user", "app/tests/crud/test_user.py::test_update_user", "app/tests/scripts/test_backend_pre_start.py::test_init_successful_connection", "app/tests/scripts/test_test_pre_start.py::test_init_successful_connection"]