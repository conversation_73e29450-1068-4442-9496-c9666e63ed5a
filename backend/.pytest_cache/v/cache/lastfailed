{"app/tests/api/routes/test_users.py::test_get_users_superuser_me": true, "app/tests/api/routes/test_users.py::test_create_user_new_email": true, "app/tests/api/routes/test_users.py::test_get_existing_user": true, "app/tests/api/routes/test_users.py::test_create_user_existing_username": true, "app/tests/api/routes/test_users.py::test_retrieve_users": true, "app/tests/api/routes/test_users.py::test_update_password_me": true, "app/tests/api/routes/test_users.py::test_update_password_me_incorrect_password": true, "app/tests/api/routes/test_users.py::test_update_password_me_same_password_error": true, "app/tests/api/routes/test_users.py::test_update_user": true, "app/tests/api/routes/test_users.py::test_update_user_not_exists": true, "app/tests/api/routes/test_users.py::test_update_user_email_exists": true, "app/tests/api/routes/test_users.py::test_delete_user_me_as_superuser": true, "app/tests/api/routes/test_users.py::test_delete_user_super_user": true, "app/tests/api/routes/test_users.py::test_delete_user_not_found": true, "app/tests/api/routes/test_users.py::test_delete_user_current_super_user_error": true, "app/tests/scripts/test_test_pre_start.py::test_init_successful_connection": true}