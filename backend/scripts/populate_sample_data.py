#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to populate sample data for spiritual coaches.
Run this script to add sample categories, locations, coaches, and reviews.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlmodel import Session, create_engine
from app.core.config import settings
from app.models import Category, Location, User, CoachProfile, Review
from app.core.security import get_password_hash
import uuid

def create_sample_categories(session: Session) -> list[Category]:
    """Create sample categories for spiritual coaching."""
    categories_data = [
        {
            "name": "Meditation & Mindfulness",
            "description": "Guidance in meditation practices, mindfulness techniques, and present-moment awareness"
        },
        {
            "name": "Life Coaching",
            "description": "Personal development, goal setting, and life transformation guidance"
        },
        {
            "name": "Energy Healing",
            "description": "Reiki, chakra balancing, and other energy healing modalities"
        },
        {
            "name": "Tarot & Oracle Reading",
            "description": "Intuitive card readings for guidance and insight"
        },
        {
            "name": "Astrology",
            "description": "Birth chart readings, astrological guidance, and cosmic insights"
        },
        {
            "name": "Crystal Healing",
            "description": "Crystal therapy, gemstone guidance, and mineral healing"
        },
        {
            "name": "Spiritual Counseling",
            "description": "Soul-centered therapy and spiritual guidance for life challenges"
        }
    ]
    
    categories = []
    for cat_data in categories_data:
        # Check if category already exists
        existing = session.query(Category).filter(Category.name == cat_data["name"]).first()
        if not existing:
            category = Category(**cat_data)
            session.add(category)
            categories.append(category)
        else:
            categories.append(existing)
    
    session.commit()
    return categories

def create_sample_locations(session: Session) -> list[Location]:
    """Create sample locations."""
    locations_data = [
        {"city": "San Francisco", "state": "California", "country": "USA"},
        {"city": "New York", "state": "New York", "country": "USA"},
        {"city": "Austin", "state": "Texas", "country": "USA"},
        {"city": "Portland", "state": "Oregon", "country": "USA"},
        {"city": "Boulder", "state": "Colorado", "country": "USA"},
        {"city": "Sedona", "state": "Arizona", "country": "USA"},
        {"city": "Los Angeles", "state": "California", "country": "USA"},
        {"city": "Miami", "state": "Florida", "country": "USA"},
    ]
    
    locations = []
    for loc_data in locations_data:
        # Check if location already exists
        existing = session.query(Location).filter(
            Location.city == loc_data["city"],
            Location.state == loc_data["state"],
            Location.country == loc_data["country"]
        ).first()
        if not existing:
            location = Location(**loc_data)
            session.add(location)
            locations.append(location)
        else:
            locations.append(existing)
    
    session.commit()
    return locations

def create_sample_coaches(session: Session, categories: list[Category], locations: list[Location]) -> list[User]:
    """Create sample coach users and their profiles."""
    coaches_data = [
        {
            "email": "<EMAIL>",
            "full_name": "Sarah Johnson",
            "bio": "Certified meditation teacher with 10 years of experience in mindfulness practices. Specializing in stress reduction and inner peace cultivation.",
            "experience_years": 10,
            "hourly_rate": 75.0,
            "category": "Meditation & Mindfulness",
            "location": ("San Francisco", "California")
        },
        {
            "email": "<EMAIL>",
            "full_name": "Michael Chen",
            "bio": "Transformational life coach helping individuals discover their purpose and achieve their dreams. Expert in goal setting and personal development.",
            "experience_years": 8,
            "hourly_rate": 90.0,
            "category": "Life Coaching",
            "location": ("New York", "New York")
        },
        {
            "email": "<EMAIL>",
            "full_name": "Luna Rodriguez",
            "bio": "Reiki Master and energy healer. Passionate about helping others balance their chakras and heal energetic blockages.",
            "experience_years": 12,
            "hourly_rate": 85.0,
            "category": "Energy Healing",
            "location": ("Sedona", "Arizona")
        },
        {
            "email": "<EMAIL>",
            "full_name": "Maya Patel",
            "bio": "Intuitive tarot reader and spiritual guide. Using ancient wisdom to provide clarity and direction for modern life challenges.",
            "experience_years": 6,
            "hourly_rate": 65.0,
            "category": "Tarot & Oracle Reading",
            "location": ("Austin", "Texas")
        },
        {
            "email": "<EMAIL>",
            "full_name": "Alex Thompson",
            "bio": "Professional astrologer specializing in natal chart readings and cosmic guidance. Helping people understand their celestial blueprint.",
            "experience_years": 15,
            "hourly_rate": 100.0,
            "category": "Astrology",
            "location": ("Los Angeles", "California")
        }
    ]
    
    coaches = []
    for coach_data in coaches_data:
        # Check if user already exists
        existing_user = session.query(User).filter(User.email == coach_data["email"]).first()
        if existing_user:
            coaches.append(existing_user)
            continue
        
        # Create user
        user = User(
            email=coach_data["email"],
            full_name=coach_data["full_name"],
            hashed_password=get_password_hash("password123"),
            role="coach",
            is_active=True
        )
        session.add(user)
        session.flush()  # Get the user ID
        
        # Find category and location
        category = next((c for c in categories if c.name == coach_data["category"]), None)
        location = next((l for l in locations 
                        if l.city == coach_data["location"][0] and l.state == coach_data["location"][1]), None)
        
        # Create coach profile
        coach_profile = CoachProfile(
            user_id=user.id,
            bio=coach_data["bio"],
            experience_years=coach_data["experience_years"],
            hourly_rate=coach_data["hourly_rate"],
            category_id=category.id if category else None,
            location_id=location.id if location else None,
            is_available=True,
            average_rating=4.5,  # Sample rating
            total_reviews=0
        )
        session.add(coach_profile)
        coaches.append(user)
    
    session.commit()
    return coaches

def main():
    """Main function to populate sample data."""
    print("Creating sample data for spiritual coaches...")
    
    # Create database engine
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    
    with Session(engine) as session:
        print("Creating categories...")
        categories = create_sample_categories(session)
        print(f"Created {len(categories)} categories")
        
        print("Creating locations...")
        locations = create_sample_locations(session)
        print(f"Created {len(locations)} locations")
        
        print("Creating sample coaches...")
        coaches = create_sample_coaches(session, categories, locations)
        print(f"Created {len(coaches)} coaches")
        
        print("Sample data creation completed!")
        print("\nYou can now:")
        print("1. Browse coaches at: GET /api/v1/coaches/coaches")
        print("2. Filter by category: GET /api/v1/coaches/coaches?category_id=<uuid>")
        print("3. Filter by location: GET /api/v1/coaches/coaches?location_id=<uuid>")
        print("4. Search coaches: GET /api/v1/coaches/coaches?search=meditation")

if __name__ == "__main__":
    main()
