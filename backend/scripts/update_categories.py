#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to update existing categories to the new spiritual coaching categories.
This will replace the old categories with the new ones.
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlmodel import Session, create_engine, select
from app.core.config import settings
from app.models import Category, CoachProfile

def update_categories(session: Session):
    """Update categories to the new spiritual coaching categories."""
    
    # New categories data
    new_categories_data = [
        {
            "name": "Mindset & Emotional Growth Coaches",
            "description": "Personal development, mindset transformation, and emotional healing guidance"
        },
        {
            "name": "Intuitive Guides & Spiritual Mentors",
            "description": "Intuitive guidance, spiritual mentorship, and soul-centered wisdom"
        },
        {
            "name": "Energy & Frequency Healing",
            "description": "Energy healing, frequency work, chakra balancing, and vibrational therapy"
        },
        {
            "name": "Holistic Wellness Practitioners",
            "description": "Holistic health approaches, mind-body-spirit wellness, and integrative healing"
        },
        {
            "name": "Ascension & Transformation Coaches",
            "description": "Spiritual ascension guidance, consciousness expansion, and transformational coaching"
        }
    ]
    
    print("Updating categories...")
    
    # Get existing categories
    existing_categories = session.exec(select(Category)).all()
    print(f"Found {len(existing_categories)} existing categories")
    
    # Delete existing categories (this will also update coach profiles to have null category_id)
    for category in existing_categories:
        print(f"Deleting category: {category.name}")
        session.delete(category)
    
    session.commit()
    print("Deleted all existing categories")
    
    # Create new categories
    new_categories = []
    for cat_data in new_categories_data:
        category = Category(**cat_data)
        session.add(category)
        new_categories.append(category)
        print(f"Created category: {cat_data['name']}")
    
    session.commit()
    print(f"Created {len(new_categories)} new categories")
    
    # Update coach profiles with new categories (mapping old to new)
    category_mapping = {
        "Meditation & Mindfulness": "Mindset & Emotional Growth Coaches",
        "Life Coaching": "Mindset & Emotional Growth Coaches", 
        "Energy Healing": "Energy & Frequency Healing",
        "Tarot & Oracle Reading": "Intuitive Guides & Spiritual Mentors",
        "Astrology": "Intuitive Guides & Spiritual Mentors",
        "Crystal Healing": "Holistic Wellness Practitioners",
        "Spiritual Counseling": "Ascension & Transformation Coaches"
    }
    
    # Get coach profiles that need category updates
    coach_profiles = session.exec(select(CoachProfile)).all()
    
    for profile in coach_profiles:
        if profile.user.email == "<EMAIL>" or profile.user.email == "<EMAIL>":
            # Update to Mindset & Emotional Growth Coaches
            new_cat = next((c for c in new_categories if c.name == "Mindset & Emotional Growth Coaches"), None)
            if new_cat:
                profile.category_id = new_cat.id
                print(f"Updated {profile.user.full_name} to category: {new_cat.name}")
        
        elif profile.user.email == "<EMAIL>" or profile.user.email == "<EMAIL>":
            # Update to Intuitive Guides & Spiritual Mentors
            new_cat = next((c for c in new_categories if c.name == "Intuitive Guides & Spiritual Mentors"), None)
            if new_cat:
                profile.category_id = new_cat.id
                print(f"Updated {profile.user.full_name} to category: {new_cat.name}")
        
        elif profile.user.email == "<EMAIL>":
            # Update to Energy & Frequency Healing
            new_cat = next((c for c in new_categories if c.name == "Energy & Frequency Healing"), None)
            if new_cat:
                profile.category_id = new_cat.id
                print(f"Updated {profile.user.full_name} to category: {new_cat.name}")
        
        elif profile.user.email == "<EMAIL>" or profile.user.email == "<EMAIL>":
            # Update to Holistic Wellness Practitioners
            new_cat = next((c for c in new_categories if c.name == "Holistic Wellness Practitioners"), None)
            if new_cat:
                profile.category_id = new_cat.id
                print(f"Updated {profile.user.full_name} to category: {new_cat.name}")
        
        elif profile.user.email == "<EMAIL>" or profile.user.email == "<EMAIL>":
            # Update to Ascension & Transformation Coaches
            new_cat = next((c for c in new_categories if c.name == "Ascension & Transformation Coaches"), None)
            if new_cat:
                profile.category_id = new_cat.id
                print(f"Updated {profile.user.full_name} to category: {new_cat.name}")
    
    session.commit()
    print("Updated coach profile categories")
    
    return new_categories

def main():
    """Main function to update categories."""
    print("🔄 Updating Spiritual Coach Categories...")
    
    # Create database engine
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    
    with Session(engine) as session:
        new_categories = update_categories(session)
        
        print(f"\n✅ Successfully updated to {len(new_categories)} new categories:")
        for category in new_categories:
            print(f"- {category.name}")
        
        print("\n🌟 New categories are now available for coach browsing!")

if __name__ == "__main__":
    main()
