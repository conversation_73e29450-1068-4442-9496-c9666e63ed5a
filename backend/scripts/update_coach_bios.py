#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update existing coach bios to match the new categories.
"""

import sys
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).resolve().parent.parent
sys.path.insert(0, str(backend_dir))

from sqlmodel import Session, create_engine, select
from app.core.config import settings
from app.models import CoachProfile, User

def update_coach_bios(session: Session):
    """Update coach bios to match new categories."""
    
    # Get all coach profiles
    coach_profiles = session.exec(
        select(CoachProfile).join(User, CoachProfile.user_id == User.id)
    ).all()
    
    print(f"Found {len(coach_profiles)} coach profiles to update")
    
    for profile in coach_profiles:
        user = profile.user
        
        if user.email == "<EMAIL>" or user.full_name == "<PERSON> Johnson":
            profile.bio = "Mindset transformation coach specializing in emotional healing and personal growth. Helping clients overcome limiting beliefs and create lasting positive change."
            print(f"Updated bio for {user.full_name}")
        
        elif user.email == "<EMAIL>" or user.full_name == "<PERSON>":
            profile.bio = "Intuitive spiritual mentor and guide. Channeling divine wisdom to help souls navigate their spiritual journey and connect with their higher purpose."
            print(f"Updated bio for {user.full_name}")
        
        elif user.email == "<EMAIL>" or user.full_name == "Luna Rodriguez":
            profile.bio = "Energy and frequency healer specializing in chakra balancing, vibrational therapy, and quantum healing. Helping clients raise their frequency and heal on all levels."
            print(f"Updated bio for {user.full_name}")
        
        elif user.email == "<EMAIL>" or user.full_name == "Maya Patel":
            profile.bio = "Holistic wellness practitioner integrating mind, body, and spirit healing. Offering comprehensive approaches to health and well-being through natural modalities."
            print(f"Updated bio for {user.full_name}")
        
        elif user.email == "<EMAIL>" or user.full_name == "Alex Thompson":
            profile.bio = "Ascension and transformation coach guiding souls through consciousness expansion and spiritual awakening. Specializing in 5D consciousness and DNA activation."
            print(f"Updated bio for {user.full_name}")
    
    session.commit()
    print("All coach bios updated successfully!")

def main():
    """Main function to update coach bios."""
    print("🔄 Updating Coach Bios...")
    
    # Create database engine
    engine = create_engine(str(settings.SQLALCHEMY_DATABASE_URI))
    
    with Session(engine) as session:
        update_coach_bios(session)
        print("✅ Coach bios updated to match new categories!")

if __name__ == "__main__":
    main()
