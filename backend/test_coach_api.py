#!/usr/bin/env python3
"""
Test script to demonstrate the spiritual coach browsing API.
"""

import requests
import json

BASE_URL = "http://localhost:8000/api/v1"

def test_browse_coaches():
    """Test browsing all coaches."""
    print("=== Testing: Browse All Coaches ===")
    response = requests.get(f"{BASE_URL}/coaches/coaches")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches")
        for coach in data['data']:
            print(f"- {coach['user']['full_name']} ({coach['category']['name'] if coach['category'] else 'No category'})")
            print(f"  Location: {coach['location']['city']}, {coach['location']['state']}" if coach['location'] else "  Location: Not specified")
            print(f"  Rate: ${coach['hourly_rate']}/hour" if coach['hourly_rate'] else "  Rate: Not specified")
            print(f"  Rating: {coach['average_rating']}/5 ({coach['total_reviews']} reviews)")
            print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def test_get_categories():
    """Test getting all categories."""
    print("=== Testing: Get Categories ===")
    response = requests.get(f"{BASE_URL}/coaches/categories")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} categories:")
        for category in data['data']:
            print(f"- {category['name']}: {category['description']}")
        print()
        return data['data']
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return []

def test_get_locations():
    """Test getting all locations."""
    print("=== Testing: Get Locations ===")
    response = requests.get(f"{BASE_URL}/coaches/locations")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} locations:")
        for location in data['data']:
            print(f"- {location['city']}, {location['state']}, {location['country']}")
        print()
        return data['data']
    else:
        print(f"Error: {response.status_code} - {response.text}")
        return []

def test_filter_by_category(categories):
    """Test filtering coaches by category."""
    if not categories:
        return
    
    # Test with the first category
    category = categories[0]
    print(f"=== Testing: Filter by Category '{category['name']}' ===")
    
    response = requests.get(f"{BASE_URL}/coaches/coaches?category_id={category['id']}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches in '{category['name']}' category:")
        for coach in data['data']:
            print(f"- {coach['user']['full_name']}")
        print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def test_filter_by_location(locations):
    """Test filtering coaches by location."""
    if not locations:
        return
    
    # Test with the first location
    location = locations[0]
    print(f"=== Testing: Filter by Location '{location['city']}, {location['state']}' ===")
    
    response = requests.get(f"{BASE_URL}/coaches/coaches?location_id={location['id']}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches in '{location['city']}, {location['state']}':")
        for coach in data['data']:
            print(f"- {coach['user']['full_name']}")
        print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def test_search_coaches():
    """Test searching coaches."""
    print("=== Testing: Search Coaches (keyword: 'meditation') ===")
    
    response = requests.get(f"{BASE_URL}/coaches/coaches?search=meditation")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches matching 'meditation':")
        for coach in data['data']:
            print(f"- {coach['user']['full_name']}")
            print(f"  Bio: {coach['bio'][:100]}..." if coach['bio'] else "  Bio: Not provided")
        print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def test_filter_by_rating():
    """Test filtering coaches by minimum rating."""
    print("=== Testing: Filter by Minimum Rating (4.0+) ===")
    
    response = requests.get(f"{BASE_URL}/coaches/coaches?min_rating=4.0")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches with rating 4.0+:")
        for coach in data['data']:
            print(f"- {coach['user']['full_name']}: {coach['average_rating']}/5")
        print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def test_filter_by_price():
    """Test filtering coaches by maximum hourly rate."""
    print("=== Testing: Filter by Maximum Price ($80/hour) ===")
    
    response = requests.get(f"{BASE_URL}/coaches/coaches?max_hourly_rate=80")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches with rate ≤ $80/hour:")
        for coach in data['data']:
            rate = coach['hourly_rate'] if coach['hourly_rate'] else 'Not specified'
            print(f"- {coach['user']['full_name']}: ${rate}/hour")
        print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def test_combined_filters():
    """Test combining multiple filters."""
    print("=== Testing: Combined Filters (meditation + max $90/hour) ===")
    
    response = requests.get(f"{BASE_URL}/coaches/coaches?search=meditation&max_hourly_rate=90")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Found {data['count']} coaches matching combined filters:")
        for coach in data['data']:
            rate = coach['hourly_rate'] if coach['hourly_rate'] else 'Not specified'
            print(f"- {coach['user']['full_name']}: ${rate}/hour")
        print()
    else:
        print(f"Error: {response.status_code} - {response.text}")

def main():
    """Run all tests."""
    print("🧘 Testing Spiritual Coach Browsing API 🧘\n")
    
    try:
        # Test basic browsing
        test_browse_coaches()
        
        # Get categories and locations for filtering tests
        categories = test_get_categories()
        locations = test_get_locations()
        
        # Test filtering
        test_filter_by_category(categories)
        test_filter_by_location(locations)
        test_search_coaches()
        test_filter_by_rating()
        test_filter_by_price()
        test_combined_filters()
        
        print("✅ All tests completed!")
        print("\n🌟 You can also test the API interactively at: http://localhost:8000/docs")
        
    except requests.exceptions.ConnectionError:
        print("❌ Error: Could not connect to the API server.")
        print("Make sure the server is running with: uvicorn app.main:app --reload")

if __name__ == "__main__":
    main()
