# Spiritual Coach Browsing Feature

This document describes the spiritual coach browsing feature that allows users to discover and connect with spiritual coaches based on various filters.

## Features

### 🧘 Coach Browsing

- Browse all available spiritual coaches
- View detailed coach profiles including bio, experience, and rates
- See coach ratings and reviews from other users

### 🔍 Advanced Filtering

- **Category Filter**: Filter by spiritual practice (Meditation, Life Coaching, Energy Healing, etc.)
- **Location Filter**: Find coaches in specific cities/states
- **Rating Filter**: Find highly-rated coaches (minimum rating)
- **Price Filter**: Filter by maximum hourly rate
- **Search**: Search by coach name or bio keywords
- **Availability**: Only show available coaches

### 📍 Categories Available

- Meditation & Mindfulness
- Mindset & Emotional Growth
- Intuitive Guides & Spiritual Mentors
- Energy Healing
- Tarot & Oracle Reading
- Astrology
- Crystal Healing
- Spiritual Counseling

## API Endpoints

### Browse Coaches

```
GET /api/v1/coaches/coaches
```

**Query Parameters:**

- `skip` (int): Number of records to skip (pagination)
- `limit` (int): Maximum number of records to return (max 100)
- `category_id` (UUID): Filter by category
- `location_id` (UUID): Filter by location
- `min_rating` (float): Minimum rating (0-5)
- `max_hourly_rate` (float): Maximum hourly rate
- `search` (string): Search in coach name and bio

**Example Requests:**

```bash
# Browse all coaches
curl "http://localhost:8000/api/v1/coaches/coaches"

# Filter by category
curl "http://localhost:8000/api/v1/coaches/coaches?category_id=<uuid>"

# Search for meditation coaches under $80/hour
curl "http://localhost:8000/api/v1/coaches/coaches?search=meditation&max_hourly_rate=80"

# Find highly-rated coaches in specific location
curl "http://localhost:8000/api/v1/coaches/coaches?location_id=<uuid>&min_rating=4.5"
```

### Get Categories

```
GET /api/v1/coaches/categories
```

### Get Locations

```
GET /api/v1/coaches/locations
```

### Get Specific Coach

```
GET /api/v1/coaches/coaches/{coach_id}
```

### Coach Reviews

```
GET /api/v1/coaches/coaches/{coach_id}/reviews
POST /api/v1/coaches/coaches/{coach_id}/reviews
```

## Coach Profile Management

### For Coaches

Coaches can create and manage their profiles:

```
POST /api/v1/coaches/coaches/profile    # Create profile
PUT /api/v1/coaches/coaches/profile     # Update profile
GET /api/v1/coaches/coaches/profile/me  # Get my profile
```

**Required Role**: `coach` or `admin`

### Profile Fields

- **Bio**: Detailed description of services and approach
- **Experience Years**: Years of experience in spiritual coaching
- **Hourly Rate**: Rate per hour in USD
- **Category**: Primary area of expertise
- **Location**: City and state where services are offered
- **Availability**: Whether currently accepting new clients
- **Profile Image**: URL to profile photo

## User Reviews

Users can leave reviews for coaches they've worked with:

### Create Review

```
POST /api/v1/coaches/coaches/{coach_id}/reviews
```

**Body:**

```json
{
  "rating": 5,
  "comment": "Amazing session! Sarah helped me find inner peace."
}
```

**Rules:**

- Users cannot review themselves
- One review per user per coach
- Rating must be 1-5 stars

## Sample Data

The system comes with sample data including:

### Sample Coaches

- **Sarah Johnson** - Meditation & Mindfulness (San Francisco, CA) - $75/hour
- **Michael Chen** - Life Coaching (New York, NY) - $90/hour
- **Luna Rodriguez** - Energy Healing (Sedona, AZ) - $85/hour
- **Maya Patel** - Tarot & Oracle Reading (Austin, TX) - $65/hour
- **Alex Thompson** - Astrology (Los Angeles, CA) - $100/hour

### Sample Categories

- Meditation & Mindfulness
- Life Coaching
- Energy Healing
- Tarot & Oracle Reading
- Astrology
- Crystal Healing
- Spiritual Counseling

## Testing

### Run Sample Data Script

```bash
cd backend
python scripts/populate_sample_data.py
```

### Run API Tests

```bash
cd backend
python test_coach_api.py
```

### Interactive Testing

Visit the Swagger UI at: http://localhost:8000/docs

## Database Schema

### New Tables

- **category**: Spiritual practice categories
- **location**: Geographic locations
- **coachprofile**: Extended profile information for coaches
- **review**: User reviews and ratings for coaches

### Relationships

- Coach profiles link to users with role 'coach'
- Reviews link coaches to users who reviewed them
- Profiles reference categories and locations

## Future Enhancements

Potential improvements for the feature:

- **Booking System**: Allow users to book sessions directly
- **Messaging**: In-app communication between users and coaches
- **Video Integration**: Built-in video calling for remote sessions
- **Payment Processing**: Integrated payment system
- **Calendar Integration**: Availability scheduling
- **Advanced Search**: More sophisticated search with tags and specializations
- **Recommendation Engine**: AI-powered coach recommendations
- **Verification System**: Coach certification verification

## Security Considerations

- Only authenticated users can leave reviews
- Coaches can only edit their own profiles
- Admins can manage categories and locations
- Input validation on all endpoints
- Rate limiting on review creation to prevent spam
