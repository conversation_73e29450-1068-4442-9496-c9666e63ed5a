import uuid
from datetime import datetime

from sqlmodel import Column, Field, Relationship, SQLModel, Text

from app.models.base import TimestampMixin


# Coach profile model (extends User for coaches)
class CoachProfile(SQLModel, TimestampMixin, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    user_id: uuid.UUID = Field(foreign_key="user.id", unique=True, nullable=False)

    # Profile information
    bio: str | None = Field(default=None, sa_column=Column(Text))
    experience_years: int | None = Field(default=None, ge=0)
    hourly_rate: float | None = Field(default=None, ge=0)
    is_available: bool = Field(default=True)
    profile_image_url: str | None = Field(default=None, max_length=500)

    # Foreign keys
    category_id: uuid.UUID | None = Field(default=None, foreign_key="category.id")
    location_id: uuid.UUID | None = Field(default=None, foreign_key="location.id")

    # Calculated fields
    average_rating: float | None = Field(default=None, ge=0, le=5)
    total_reviews: int = Field(default=0, ge=0)

    # Relationships
    user: "User" = Relationship()
    category: "Category" | None = Relationship(back_populates="coach_profiles")
    location: "Location" | None = Relationship(back_populates="coach_profiles")
    reviews: list["Review"] = Relationship(back_populates="coach_profile")
