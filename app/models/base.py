import uuid
from datetime import datetime

from sqlmodel import Field, SQLModel


class BaseModel(SQLModel):
    """Base model with common fields for all database models."""
    
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)


class TimestampMixin(SQLModel):
    """Mixin for models that need timestamp fields."""
    
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)
