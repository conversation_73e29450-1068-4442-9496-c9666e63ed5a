import enum
import uuid

from pydantic import EmailStr
from sqlmodel import CheckConstraint, Column, Field, Relationship, SQLModel, String

from app.models.base import TimestampMixin


class UserRole(str, enum.Enum):
    USER = "user"
    COACH = "coach"
    ADMIN = "admin"


# Database model, database table inferred from class name
class User(SQLModel, TimestampMixin, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    email: EmailStr = Field(unique=True, index=True, max_length=255)
    hashed_password: str
    is_active: bool = True
    is_superuser: bool = False
    full_name: str | None = Field(default=None, max_length=255)
    
    # Use string type with check constraint
    role: str = Field(
        default=UserRole.USER.value,
        sa_column=Column(
            String(50),
            CheckConstraint("role IN ('user', 'coach', 'admin')"),
            nullable=False,
        ),
    )
    
    # Relationships
    items: list["Item"] = Relationship(back_populates="owner", cascade_delete=True)
