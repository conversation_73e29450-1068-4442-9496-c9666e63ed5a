import uuid

from sqlmodel import Field, Relationship, SQLModel

from app.models.base import TimestampMixin


# Database model, database table inferred from class name
class Item(SQLModel, TimestampMixin, table=True):
    id: uuid.UUID = Field(default_factory=uuid.uuid4, primary_key=True)
    title: str = Field(max_length=255)
    description: str | None = Field(default=None, max_length=255)
    owner_id: uuid.UUID = Field(
        foreign_key="user.id", nullable=False, ondelete="CASCADE"
    )
    
    # Relationships
    owner: "User" | None = Relationship(back_populates="items")
