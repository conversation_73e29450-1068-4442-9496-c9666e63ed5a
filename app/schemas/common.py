from sqlmodel import SQLModel


# Generic message
class Message(SQLModel):
    message: str


# Pagination response wrapper
class PaginatedResponse(SQLModel):
    count: int
    
    
# JSON payload containing access token
class Token(SQLModel):
    access_token: str
    token_type: str = "bearer"


# Contents of JWT token
class TokenPayload(SQLModel):
    sub: str | None = None


class NewPassword(SQLModel):
    token: str
    new_password: str
