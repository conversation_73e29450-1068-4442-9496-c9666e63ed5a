import uuid

from sqlmodel import Session

from app.models.location import Location
from app.repositories.location_repository import LocationRepository


class LocationService:
    """Business logic for location operations."""
    
    def __init__(self):
        self.location_repo = LocationRepository()
    
    def get_all_locations(self, session: Session, *, skip: int = 0, limit: int = 100) -> list[Location]:
        """Get all locations."""
        return self.location_repo.get_multi(session, skip=skip, limit=limit)
    
    def get_location_by_id(self, session: Session, *, location_id: uuid.UUID) -> Location | None:
        """Get location by ID."""
        return self.location_repo.get(session, location_id)
    
    def create_location(self, session: Session, *, city: str, state: str, country: str) -> Location:
        """Create a new location."""
        # Check if location already exists
        existing = self.location_repo.get_by_city_state(session, city=city, state=state, country=country)
        if existing:
            raise ValueError(f"Location '{city}, {state}, {country}' already exists")
        
        return self.location_repo.create_location(session, city=city, state=state, country=country)
