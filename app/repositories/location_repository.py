from sqlmodel import Session, select

from app.models.location import Location
from app.repositories.base_repository import BaseRepository


class LocationRepository(BaseRepository[Location, dict, dict]):
    """Repository for Location model."""
    
    def __init__(self):
        super().__init__(Location)
    
    def get_by_city_state(self, session: Session, *, city: str, state: str, country: str) -> Location | None:
        """Get location by city, state, and country."""
        statement = select(Location).where(
            Location.city == city,
            Location.state == state,
            Location.country == country
        )
        return session.exec(statement).first()
    
    def create_location(self, session: Session, *, city: str, state: str, country: str) -> Location:
        """Create a new location."""
        db_obj = Location(city=city, state=state, country=country)
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
