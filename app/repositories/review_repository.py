import uuid

from sqlmodel import Session, func, select

from app.models.review import Review
from app.repositories.base_repository import BaseRepository
from app.schemas.review import ReviewCreate


class ReviewRepository(BaseRepository[Review, ReviewCreate, dict]):
    """Repository for Review model."""
    
    def __init__(self):
        super().__init__(Review)
    
    def get_by_coach(
        self, session: Session, *, coach_profile_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[Review]:
        """Get reviews for a specific coach."""
        statement = (
            select(Review)
            .where(Review.coach_profile_id == coach_profile_id)
            .order_by(Review.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def get_by_user(
        self, session: Session, *, user_id: uuid.UUID, skip: int = 0, limit: int = 100
    ) -> list[Review]:
        """Get reviews by a specific user."""
        statement = (
            select(Review)
            .where(Review.user_id == user_id)
            .order_by(Review.created_at.desc())
            .offset(skip)
            .limit(limit)
        )
        return session.exec(statement).all()
    
    def get_rating_stats(self, session: Session, *, coach_profile_id: uuid.UUID) -> tuple[float | None, int]:
        """Get average rating and total count for a coach."""
        result = session.exec(
            select(func.avg(Review.rating), func.count(Review.id)).where(
                Review.coach_profile_id == coach_profile_id
            )
        ).first()
        
        if result and result[1] > 0:
            return round(result[0], 2), result[1]
        return None, 0
    
    def create_review(self, session: Session, *, user_id: uuid.UUID, review_in: ReviewCreate) -> Review:
        """Create a new review."""
        db_obj = Review(user_id=user_id, **review_in.model_dump())
        session.add(db_obj)
        session.commit()
        session.refresh(db_obj)
        return db_obj
