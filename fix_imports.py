#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to fix import issues after implementing the coach profile feature.
"""

import os
import re

def fix_api_main():
    """Fix the API main file to remove roles import."""
    file_path = "app/api/main.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Remove roles from import
    content = re.sub(r', roles', '', content)
    content = re.sub(r'roles, ', '', content)
    
    # Remove roles router include
    content = re.sub(r'api_router\.include_router\(roles\.router.*?\)\n', '', content)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed app/api/main.py")

def fix_coaches_routes():
    """Fix the coaches routes to use the new CRUD structure."""
    file_path = "app/api/routes/coaches.py"
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    # Replace crud_coaches with crud
    content = content.replace('from app import crud_coaches', 'from app import crud')
    content = content.replace('crud_coaches.', 'crud.')
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    print("✅ Fixed app/api/routes/coaches.py")

def main():
    """Main function to fix all import issues."""
    print("🔧 Fixing import issues...")
    
    try:
        fix_api_main()
        fix_coaches_routes()
        print("\n✅ All import issues fixed!")
        print("🚀 You can now restart the server with: uvicorn app.main:app --reload")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
