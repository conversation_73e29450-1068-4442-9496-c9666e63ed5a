import { TanStackRouterVite } from "@tanstack/router-vite-plugin";
import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vite";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react(), TanStackRouterVite()],
  server: {
    host: "0.0.0.0",  // Makes the app accessible on the public IP
    port: 3000,       // Specify the port you want to use for dev server
    strictPort: true, // Ensures the port is used even if it's occupied
    allowedHosts: ['hi-vi.com', 'localhost', '127.0.0.1']
  },
  preview: {
    port: 3000, // Set the port for the production preview
    host: "0.0.0.0", // Ensures it's accessible on the public IP for preview
  },
});