{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -p tsconfig.build.json && vite build", "lint": "biome check --apply-unsafe --no-errors-on-unmatched --files-ignore-unknown=true ./", "preview": "vite preview", "generate-client": "openapi-ts"}, "dependencies": {"@chakra-ui/icons": "2.1.1", "@chakra-ui/react": "2.8.2", "@emotion/react": "11.11.3", "@emotion/styled": "11.11.0", "@tanstack/react-query": "^5.28.14", "@tanstack/react-query-devtools": "^5.28.14", "@tanstack/react-router": "^1.19.1", "axios": "1.7.4", "form-data": "4.0.0", "framer-motion": "10.16.16", "frontend": "file:", "js-cookie": "^3.0.5", "react": "^18.3.1", "react-currency-input-field": "^3.10.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.13", "react-hook-form": "7.49.3", "react-icons": "5.0.1", "react-range-slider-input": "^3.2.1", "react-select": "^5.10.0"}, "devDependencies": {"@biomejs/biome": "1.6.1", "@hey-api/openapi-ts": "^0.57.0", "@playwright/test": "^1.45.2", "@tanstack/router-devtools": "1.19.1", "@tanstack/router-vite-plugin": "1.19.0", "@types/node": "^20.10.5", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react-swc": "^3.5.0", "dotenv": "^16.4.5", "typescript": "^5.2.2", "vite": "^5.4.14"}}