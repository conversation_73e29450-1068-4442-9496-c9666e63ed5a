import React, { useState } from "react";
import "../../styles/profile/image_description.css";

function ImageDescription() {
  const images = [
    "/assets/images/profile/imageSelect.png",
    "/assets/images/profile/image1.png",
    "/assets/images/profile/image2.png",
    "/assets/images/profile/image3.png",
    "/assets/images/profile/image4.png",
  ];
  const star = [
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/StarWhite.svg",
  ];
  const colors = [
    { name: "Green", color: "rgba(33, 149, 66, 1)" },
    { name: "Yellow", color: "rgba(244, 209, 24, 1)" },
    { name: "Primary", color: "rgba(105, 58, 240, 1)" },
  ];
  const [selectedImage, setSelectedImage] = useState(images[0]);
  const [selectedColor, setSelectedColor] = useState(colors[1]);
  const [time, setTime] = useState(1);

  const increaseTime = () => {
    setTime((prevTime) => prevTime + 1);
  };

  const decreaseTime = () => {
    if (time > 0) {
      setTime((prevTime) => prevTime - 1);
    }
  };
  return (
    <div className="container_img_description">
      <div className="container_list_img">
        <div className="container_img_selects">
          <img src={selectedImage} alt="Main view" className="img_select" />
        </div>
        <div className="container_list_img_select">
          {images.map((img, index) => (
            <div className="container_img_items" key={index}>
              <img
                src={img}
                alt={`Thumbnail ${index}`}
                className={`thumbnail ${selectedImage === img ? "active" : ""}`}
                onClick={() => setSelectedImage(img)}
              />
            </div>
          ))}
        </div>
      </div>
      <div className="container_description_Img">
        <div className="container_title_price">
          <div className="container_title_vote">
            <div className="container_vote_star">
              <div className="container_star">
                {star.map((img, index) => (
                  <img key={index} src={img} alt="Star" className="star" />
                ))}
              </div>
              <p className="text_vote">(1.234)</p>
            </div>
            <p className="text_title">Samatha J. Doe</p>
          </div>
          <div className="container_price">
            <p className="text_price">$240/</p>
            <p className="text_hour">hr</p>
          </div>
          <p className="text_description_img">
            Samantha is a spiritual coach and energy healer who helps clients clear blockages, align with their higher selves, and navigate their spiritual journeys with personalized guidance and transformative healing.
          </p>
        </div>
        <div className="container_type_color">
          <div className="container_text_type">
            <p className="text_type">Type:</p>
            <p className="text_type_select">{selectedColor.name}</p>
          </div>
          <div className="container_list_color">
            {colors.map((color, index) => (
              <div
                className={`background_Color ${selectedColor.name === color.name ? "container_select_color" : "container_color"}`}
                key={index}
              >
                <div
                  className="container_color"
                  style={{ backgroundColor: color.color }}
                  onClick={() => setSelectedColor(colors[index])}
                />
              </div>
            ))}
          </div>
        </div>
        <div className="container_time">
          <p className="text_hours">Hours:</p>
          <div className="container_btn_change_time">
            <img
              src="/assets/images/profile/minus.svg"
              className="icon_24"
              alt="minus"
              onClick={decreaseTime}
            />
            <p className="text_number_time">{time} Hours</p>
            <img
              src="/assets/images/profile/plus.svg"
              className="icon_24"
              alt="plus"
              onClick={increaseTime}
            />
          </div>
        </div>
        <div className="container_btn_buy_cart">
          <div className="container_btn_add_cart">
            <div className="btn_add_cart">
              <p className="text_add_cart">Add To Cart - $240</p>
            </div>
            <div className="btn_items_comp_fav">
              <img
                src="/assets/images/profile/GitDiff.svg"
                alt="compare"
                className="img_24"
              />
            </div>
            <div className="btn_items_comp_fav">
              <img
                src="/assets/images/profile/Heart.svg"
                alt="compare"
                className="img_24"
              />
            </div>
          </div>
          <div className="container_btn_buy_items">
            <p className="text_buy">Buy It Now</p>
          </div>
        </div>
      </div>
    </div>
  );
}
export default ImageDescription;
