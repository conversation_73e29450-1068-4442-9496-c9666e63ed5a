import React, { useState } from "react";
import "../../styles/profile/products_details.css";
function ProductsDetails({ product }: { product: any }) {
  const [isTags, setIsTags] = useState(false);
  const ratings = [
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/StarWhite.svg",
  ];

  return (
    <>
      <div className="container_products">
        <div className="container_img_products">
          <img src={product.img} alt="Product" className="img_products" />
          <div className="container_tag_products">
            <img
              src={
                isTags
                  ? "/assets/images/profile/BookmarksSelect.svg"
                  : "/assets/images/profile/BookmarksNoSelect.svg"
              }
              alt="Product"
              className="icon_24"
              onClick={() => setIsTags(!isTags)}
            />
          </div>
          <div className="container_name_products">
            <p className="text_tags_products">{product.nameTags}</p>
          </div>
        </div>
        <div className="container_details_products">
          <div className="container_title_products">
            <div className="container_title_items_products">
              <p className="text_Caption02">Available for Readings Now</p>
              <p className="text_Heading6">{product.title}</p>
            </div>
            <div className="container_number_consultations">
              <div className="container_popular_products">
                <img
                  src="/assets/images/profile/ArrowUp.svg"
                  alt="Product"
                  className="img_16"
                />
                <p className="text_Caption01">Popular</p>
              </div>
              <div className="line_height_Outline"></div>
              <div className="container_text_number_consultations">
                <p className="text_ButtonSmall">{product.countConsultations}</p>
                <p className="text_Caption01">Consultations</p>
              </div>
            </div>
          </div>
          <div className="container_avt_ratings">
            <img src={product.avatar} alt="Product" className="avatar_40" />
            <div className="container_name_ratings">
              <p className="text_Button">{product.name}</p>
              <div className="container_number_ratings">
                <div className="container_star_ratings">
                  {ratings.map((rating, index) => (
                    <img
                      key={index}
                      src={rating}
                      alt="star"
                      className="icon_12"
                    />
                  ))}
                </div>
                <p className="text_Caption02">(32)</p>
              </div>
            </div>
          </div>
          <div className="container_line_width"></div>
          <div className="container_price_btnConnect">
            <div className="container_price_hour">
              <p className="text_Heading05">$24/</p>
              <p className="text_Body2">hr</p>
            </div>
            <div
              className={
                isTags ? "container_btnConnectTags" : "container_btnConnect"
              }
            >
              <p
                className={
                  isTags ? "text_ButtonSmallWhite" : "text_ButtonSmall"
                }
              >
                Connect
              </p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
export default ProductsDetails;
