import React, { useState } from "react";
import "../../styles/profile/rating_reviews.css";
function RatingReviews() {
  const ratings = [
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/Star.svg",
    "/assets/images/profile/StarWhite.svg",
  ];
  const ratingsWrite = [
    "/assets/images/profile/favorite_major.svg",
    "/assets/images/profile/favorite_major.svg",
    "/assets/images/profile/favorite_major.svg",
    "/assets/images/profile/favorite_major.svg",
    "/assets/images/profile/favorite_major.svg",
  ];
  const ratingsPercent = [
    { stars: 5, percent: 60 },
    { stars: 4, percent: 20 },
    { stars: 3, percent: 10 },
    { stars: 2, percent: 7 },
    { stars: 1, percent: 3 },
  ];
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState("Most Recent");

  const options = ["Most Recent", "Top Rated", "Lowest Rated"];
  return (
    <>
      <div className="container_rating_reviews">
        <div className="container_rating_btn_write">
          <div className="container_average_point">
            <p className="text_Heading01">4.8</p>
            <div className="container_star_ratings">
              {ratings.map((rating, index) => (
                <img key={index} src={rating} alt="star" className="img_24" />
              ))}
            </div>
            <p className="text_bodyDefault">(232 Ratings)</p>
          </div>
          <div className="container_percent_rating">
            {ratingsPercent.map((rating) => (
              <div className="rating-row" key={rating.stars}>
                <span className="text_Button">{rating.stars}</span>
                <img src="/assets/images/profile/Star.svg" className="img_20" />
                <div className="progress-bar">
                  <div
                    className="fill"
                    style={{ width: `${rating.percent}%` }}
                  ></div>
                </div>
                <span className="text_percent">{rating.percent}%</span>
              </div>
            ))}
          </div>
          <div className="container_btn_write">
            <p className="text_Button">Write a Review</p>
          </div>
        </div>
        {/* container_percent_rating mobile */}
        <div className="container_percent_rating_mobile">
          {ratingsPercent.map((rating) => (
            <div className="rating-row" key={rating.stars}>
              <span className="text_Button">{rating.stars}</span>
              <img src="/assets/images/profile/Star.svg" className="img_20" />
              <div className="progress-bar">
                <div
                  className="fill"
                  style={{ width: `${rating.percent}%` }}
                ></div>
              </div>
              <span className="text_percent">{rating.percent}%</span>
            </div>
          ))}
        </div>
        {/* List-cmt */}
        <div className="container_list_comment">
          <div className="container_total_filter_cmt">
            <p className="text_totalCmt">03 Comments</p>
            <div className="container_filter_cmt">
              <p className="text_SortBy">Sort by:</p>
              <div className="filter-dropdown">
                <div
                  className="container_btn_filter_cmt"
                  onClick={() => setIsOpen(!isOpen)}
                >
                  <p
                    className="text_bodyDefault"
                    style={{ whiteSpace: "nowrap" }}
                  >
                    {selectedOption}
                  </p>
                  <img
                    src="/assets/images/profile/CaretDown.svg"
                    className={`icon_20 ${isOpen ? "rotate" : ""}`}
                    alt="chevron"
                  />
                </div>

                {/* List select filter */}
                {isOpen && (
                  <div className="dropdown-menu">
                    {options.map((option) => (
                      <div
                        key={option}
                        className="dropdown-item"
                        onClick={() => {
                          setSelectedOption(option);
                          setIsOpen(false);
                        }}
                      >
                        {option}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>
          {/* Comment */}
          <div className="container_list_cmt">
            {/* item */}
            <div className="container_item_comment">
              <div className="container_avatar_rating_cmt">
                <div className="container_avatar_cmt">
                  <img
                    src="/assets/images/profile/avatarCmt1.png"
                    alt="avt"
                    className="avatar_cmt"
                  />
                </div>
                <div className="container_rating_cmt">
                  <div className="container_rating_user">
                    <p className="text_Button">Guy Hawkins</p>
                    <div className="container_star_ratings">
                      {ratings.map((rating, index) => (
                        <img
                          key={index}
                          src={rating}
                          alt="star"
                          className="img_12"
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text_Caption01">1 days ago</p>
                </div>
              </div>
              <p className="text_cmt ">
                The session was deeply intuitive and personalized, leaving me
                with immense clarity and peace. I highly recommend this service
                to anyone seeking profound wisdom and direction in their life.
              </p>
              <div className="container_list_img_cmt">
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
              </div>
              <div className="container_count_like">
                <img
                  src="/assets/images/profile/ThumbsUp.svg"
                  className="icon_20"
                />
                <p className="text_Button">12</p>
                <p className="text_bodyDefaultSecondary">
                  people found this helpful
                </p>
              </div>
            </div>
            {/* item */}
            <div className="container_item_comment">
              <div className="container_avatar_rating_cmt">
                <div className="container_avatar_cmt">
                  <img
                    src="/assets/images/profile/avatarCmt1.png"
                    alt="avt"
                    className="avatar_cmt"
                  />
                </div>
                <div className="container_rating_cmt">
                  <div className="container_rating_user">
                    <p className="text_Button">Guy Hawkins</p>
                    <div className="container_star_ratings">
                      {ratings.map((rating, index) => (
                        <img
                          key={index}
                          src={rating}
                          alt="star"
                          className="img_12"
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text_Caption01">1 days ago</p>
                </div>
              </div>
              <p className="text_cmt ">
                The session was deeply intuitive and personalized, leaving me
                with immense clarity and peace. I highly recommend this service
                to anyone seeking profound wisdom and direction in their life.
              </p>
              <div className="container_list_img_cmt">
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
              </div>
              <div className="container_count_like">
                <img
                  src="/assets/images/profile/ThumbsUp.svg"
                  className="icon_20"
                />
                <p className="text_Button">12</p>
                <p className="text_bodyDefaultSecondary">
                  people found this helpful
                </p>
              </div>
            </div>
            {/* item */}
            <div className="container_item_comment">
              <div className="container_avatar_rating_cmt">
                <div className="container_avatar_cmt">
                  <img
                    src="/assets/images/profile/avatarCmt1.png"
                    alt="avt"
                    className="avatar_cmt"
                  />
                </div>
                <div className="container_rating_cmt">
                  <div className="container_rating_user">
                    <p className="text_Button">Guy Hawkins</p>
                    <div className="container_star_ratings">
                      {ratings.map((rating, index) => (
                        <img
                          key={index}
                          src={rating}
                          alt="star"
                          className="img_12"
                        />
                      ))}
                    </div>
                  </div>
                  <p className="text_Caption01">1 days ago</p>
                </div>
              </div>
              <p className="text_cmt ">
                The session was deeply intuitive and personalized, leaving me
                with immense clarity and peace. I highly recommend this service
                to anyone seeking profound wisdom and direction in their life.
              </p>
              <div className="container_list_img_cmt">
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
                <div className="container_img_cmt">
                  <img
                    src="/assets/images/profile/imgCmt1.png"
                    alt="img"
                    className="icon_80"
                  />
                </div>
              </div>
              <div className="container_count_like">
                <img
                  src="/assets/images/profile/ThumbsUp.svg"
                  className="icon_20"
                />
                <p className="text_Button">12</p>
                <p className="text_bodyDefaultSecondary">
                  people found this helpful
                </p>
              </div>
            </div>
          </div>
        </div>
        {/* Write a review */}
        <div className="container_write_review">
          <div className="container_rating_star">
            <p className="text_Heading04">Write a review:</p>
            <div className="container_star_ratings">
              {ratingsWrite.map((rating, index) => (
                <img key={index} src={rating} alt="star" className="img_40" />
              ))}
            </div>
          </div>
          <div className="container_input_submit_review">
            <div className="container_input_item_review">
              <p className="text_bodyDefaultReview">Review Title</p>
              <input
                type="text"
                placeholder="Give your review a title"
                className="input_review"
              />
            </div>
            <div className="container_input_item_review">
              <p className="text_bodyDefaultReview">Review </p>
              <textarea
                placeholder="Write your comment here"
                className="input_area_review"
              />
            </div>
            <div className="container_input_name_email">
              <input
                type="text"
                placeholder="You Name (Public)"
                className="input_name_email"
              />
              <input
                type="text"
                placeholder="Your email (private)"
                className="input_name_email"
              />
            </div>
            <div className="container_save_information">
              <input type="checkbox" className=" checkboxSaveInformation" />
              <p className="text_save_information">
                Save my name, email, and website in this browser for the next
                time I comment.
              </p>
            </div>
            <div className="btn_submit_reviews">
              <p className="text_SubmitReviews">Submit Reviews</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
export default RatingReviews;
