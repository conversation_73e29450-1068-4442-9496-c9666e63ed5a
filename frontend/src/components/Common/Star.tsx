import React from "react";
import styles from "../../styles/common/star.module.css";
type Props = {
	value?: number
}
const Star = ({value}:Props) => {
    value=value?value:5
    let width1:number = value>=1?100:value/1*100,
        width2:number=value>=2?100:(value-1)/1*100,
        width3:number=value>=3?100:(value-2)/1*100,
        width4:number=value>=4?100:(value-3)/1*100,
        width5:number=value>=5?100:(value-4)/1*100
  return (
    
    <div id="starfield" className={styles.box_star}>
          <div className={`${styles.star}`}>
            <i className={`fa fa-star`} aria-hidden="true"></i>
            <i className={`fa fa-star ${styles.star_mask}`} aria-hidden="true" style={{width:`${width1}%`}}></i>
          </div>
          <div className={`${styles.star}`}>
            <i className={`fa fa-star`} aria-hidden="true"></i>
            <i className={`fa fa-star ${styles.star_mask}`} aria-hidden="true" style={{width:`${width2}%`}}></i>
          </div>
          <div className={`${styles.star}`}>
            <i className={`fa fa-star`} aria-hidden="true"></i>
            <i className={`fa fa-star ${styles.star_mask}`} aria-hidden="true" style={{width:`${width3}%`}}></i>
          </div>
          <div className={`${styles.star}`}>
            <i className={`fa fa-star`} aria-hidden="true"></i>
            <i className={`fa fa-star ${styles.star_mask}`} aria-hidden="true" style={{width:`${width4}%`}}></i>
          </div>
          <div className={`${styles.star}`}>
            <i className={`fa fa-star`} aria-hidden="true"></i>
            <i className={`fa fa-star ${styles.star_mask}`} aria-hidden="true" style={{width:`${width5}%`}}></i>
          </div>
        </div>
  );
};

export default Star;
