import React from "react";
import styles from "../../styles/discover/main.module.css";
import Select, { StylesConfig,DropdownIndicatorProps  } from 'react-select'
import RangeSlider from 'react-range-slider-input';
import 'react-range-slider-input/dist/style.css';
import CurrencyInput from 'react-currency-input-field';
import Star from "../Common/Star"
const Main = () => {
    
    
    const colourStyles: StylesConfig = {
        control: (baseStyles,state) => ({ ...baseStyles, paddingTop:'22px',paddingLeft:'4px', height: '100%',borderRadius: '12px',borderColor: state.isFocused ? '#693AF0 !important' : '#E4E4E4 !important', }),
        indicatorSeparator: (baseStyles,state) => ({ display:'none', }),
        indicatorsContainer: (baseStyles,state) => ({ display:'none', })    
    };
    const DropdownIndicator = (
        props: DropdownIndicatorProps
      ) => {
        return (
          <DropdownIndicator {...props}>
            
          </DropdownIndicator>
        );
      };
    const options = {
        location: [
            {value:1,label:'Location 1'},
            {value:2,label:'Location 2'},
            {value:3,label:'Location 3'},
            {value:4,label:'Location 4'},
            {value:5,label:'Location 5'},
            {value:6,label:'Location 6'},
        ],
        service: [
            {value:1,label:'Service 1'},
            {value:2,label:'Service 2'},
            {value:3,label:'Service 3'},
            {value:4,label:'Service 4'},
            {value:5,label:'Service 5'},
            {value:6,label:'Service 6'},
        ],
        timeFrame:[
            {value:1,label:'Anytime',total:12},
            {value:2,label:'Last week',total:12},
            {value:3,label:'Last month',total:12},
        ],
        online:[
            {value:1,label:'Remote',total:12},
            {value:2,label:'Hybrid',total:12},
            {value:3,label:'On Site',total:12},
        ],
        locationFilter:[
            {value:1,label:'Bay Area',total:12},
            {value:2,label:'New York City',total:12},
            {value:3,label:'Los Angeles',total:12},
        ],
        specialy:[
            {value:1,label:'Esoteric Sciences',total:12},
            {value:2,label:'Divination Tools',total:12},
            {value:3,label:'Psychic Abilities',total:12},
            {value:4,label:'Healing',total:12},
            {value:5,label:'Sacred Sites Travel',total:12},
        ],
        readingType:[
            {value:1,label:'Western Astrology',total:12},
            {value:2,label:'Vedic Astrology',total:12},
            {value:3,label:'Chinese Astrology',total:12},
            {value:4,label:'Numerology',total:12},
            {value:5,label:'Palmistry',total:12},
        ],
    }
    const listFeatured = [
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            consultations: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24
        },
    ]
    const listAvailable = [
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            visit: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24,
            listTag: ['$150K-250K','Instant Insight','Top Seer Reviews']
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            visit: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24,
            listTag: ['$150K-250K','Instant Insight','Top Seer Reviews']
        },
        {
            image:'/assets/images/home/<USER>',
            name: 'Ralph Edwards',
            avatar: '/assets/images/discover/avatar.jpg',
            visit: 450,
            cate: 'Mystic Minds',
            star: 4.8,
            totalRate: 32,
            price: 24,
            listTag: ['$150K-250K','Instant Insight','Top Seer Reviews']
        }
    ]
  return (
    <div className={styles.main_discover}>
        <div className={styles.container_discover}>
            <div className={`${styles.box_filter} ${styles.hide}`}>
                <div className={styles.container_filter}>
                    <p className={styles.head_filter}>Time Frame</p>
                    <div className={styles.item_filter}>
                    {
                        options.timeFrame.map((item:any,i:number)=>(
                            <label key={i} className={styles.item}><div className={styles.name_item}><p>{item.label}</p><p>({item.total})</p></div>
                                <input type="radio" name="radio_time" value={item.value}/>
                                <div className={styles.checkmark}></div>
                            </label>
                        ))
                    }
                    </div>
                </div>
                <hr />
                <div className={styles.container_filter}>
                    <p className={styles.head_filter}>Price Range</p>
                    <div className={styles.item_filter}>
                        <div className={styles.ranger}>
                            <RangeSlider id="range-slider-purple" min={0} max={100} step={5}/>
                        </div>
                        <div className={styles.value_ranger}>
                            <div className={styles.item_value}>
                                <p className={styles.text_value}>Min price</p>
                                <div className={styles.show_value}>
                                    <CurrencyInput className={styles.input_value} prefix="$" value={123} />
                                    <span className={styles.hr}>hr</span>
                                </div>
                            </div>
                            <div className={styles.item_value}>
                                <p className={styles.text_value}>Min price</p>
                                <div className={styles.show_value}>
                                    <CurrencyInput className={styles.input_value} prefix="$" value={123} />
                                    <span className={styles.hr}>hr</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr />
                <div className={styles.container_filter}>
                    <p className={styles.head_filter}>Online Availability</p>
                    <div className={styles.item_filter}>
                    {
                        options.online.map((item:any,i:number)=>(
                            <label key={i} className={styles.item}><div className={styles.name_item}><p>{item.label}</p><p>({item.total})</p></div>
                                <input type="radio" name="radio_online" value={item.value}/>
                                <div className={styles.checkmark}></div>
                            </label>
                        ))
                    }
                    </div>
                </div>
                <hr />
                <div className={styles.container_filter}>
                    <p className={styles.head_filter}>Location</p>
                    <div className={styles.item_filter}>
                    {
                        options.locationFilter.map((item:any,i:number)=>(
                            <label key={i} className={styles.item}><div className={styles.name_item}><p>{item.label}</p><p>({item.total})</p></div>
                                <input type="radio" name="radio_local" value={item.value}/>
                                <div className={styles.checkmark}></div>
                            </label>
                        ))
                    }
                    </div>
                </div>
                <hr />
                <div className={styles.container_filter}>
                    <p className={styles.head_filter}>Specialty</p>
                    <div className={styles.item_filter}>
                    {
                        options.specialy.map((item:any,i:number)=>(
                            <label key={i} className={styles.item}><div className={styles.name_item}><p>{item.label}</p><p>({item.total})</p></div>
                                <input type="radio" name="radio_specialy" value={item.value}/>
                                <div className={styles.checkmark}></div>
                            </label>
                        ))
                    }
                    </div>
                </div>
                <hr />
                <div className={styles.container_filter}>
                    <p className={styles.head_filter}>Reading Types</p>
                    <div className={styles.item_filter}>
                    {
                        options.readingType.map((item:any,i:number)=>(
                            <label key={i} className={styles.item}><div className={styles.name_item}><p>{item.label}</p><p>({item.total})</p></div>
                                <input type="radio" name="radio_reading" value={item.value}/>
                                <div className={styles.checkmark}></div>
                            </label>
                        ))
                    }
                    </div>
                </div>
            </div>
            <div className={styles.box_main}>
                <div className={styles.box_search}>
                    <div className={`${styles.container_item_select} ${styles.select_location}`}>
                        <Select className={`${styles.item_select}`} options={options.location} styles={colourStyles} placeholder="Location"/>
                        <img className={styles.icon} src="/assets/images/discover/MapPin.svg" alt="icon" />
                    </div>
                    <div className={`${styles.container_item_select} ${styles.select_service}`}>
                        <Select className={`${styles.item_select}`} options={options.service} styles={colourStyles} placeholder="All Model"/>
                        <img className={styles.icon} src="/assets/images/discover/CaretDown.svg" alt="icon" />
                    </div>
                
                <button className={styles.btn_search}><img src="/assets/images/discover/icon_search.svg" alt="search" />Search</button>
                </div>
                <div className={styles.box_content}>
                    <p className={styles.headcontent}></p>
                    <div className={styles.headcontent}>
                        <p className={styles.text}>Featured Coaches</p>
                        <button className={styles.btn_filter}>
                            <img src="/assets/images/discover/icon_filter.svg" alt="filter" />
                            Filters
                        </button>
                    </div>
                    <div className={styles.box_list_featured}>
                        <div className={styles.list_featured}>
                        {
                            listFeatured.map((item:any,i:number)=>(
                                <a href="/profile" target="_blank" key={i} className={styles.item_featured}>
                                    <div className={styles.box_img}>
                                        <img src={item.image} alt="image" />
                                        <button className={styles.btn_save}><img src={item.isSaved?'/assets/images/discover/icon_save_active.svg':'/assets/images/discover/icon_save.svg'} alt="save" /></button>
                                        <div className={styles.cate}>{item.cate}</div>
                                    </div>
                                    <div className={styles.box_info}>
                                        <p className={styles.sub_title}>Available for Readings Now</p>
                                        <p className={styles.title}>Top Spiritual Coaches</p>
                                        <div className={styles.box_popular}>
                                            <div className={styles.popular}>
                                                <img src="/assets/images/discover/ArrowUp.svg" alt="popular" />
                                                <span className={styles.text}>Popular</span>
                                            </div>
                                            <div className={styles.line}></div>
                                            <div className={styles.box_consul}>
                                                <p className={styles.number}>{item.consultations}</p>
                                                <p className={styles.text}>Consultations</p>
                                            </div>
                                        </div>
                                        <div className={styles.box_profile}>
                                            <div className={styles.box_avatar}>
                                                <img src={item.avatar} alt={item.name} />
                                            </div>
                                            <div className={styles.box_name}>
                                                <p className={styles.name}>{item.name}</p>
                                                <div className={styles.box_star}>
                                                    <Star value={Number(item.star)}/>
                                                    <span className={styles.total_rate}>({item.totalRate})</span>
                                                </div>
                                            </div>
                                        </div>
                                        <hr/>
                                        <div className={styles.box_price}>
                                            <div className={styles.price}>
                                                <p className={styles.numb}>${item.price}/</p>
                                                <p className={styles.text}>hr</p>
                                            </div>
                                            <a className={styles.connect} href="#">Connect</a>
                                        </div>
                                    </div>
                                </a>
                            ))
                        }
                        </div>
                    </div>
                    
                </div>
                <div className={styles.box_content}>
                    <div className={styles.headcontent}>
                        <p className={styles.text}>Available Coaches</p>
                    </div>
                    <div className={styles.box_list_available}>
                        <div className={styles.list_available}>
                        {
                            listAvailable.map((item:any,i:number)=>(
                                <a href="/profile" target="_blank" key={i} className={styles.item_available}>
                                    <div className={styles.box_img}>
                                        <img className={styles.image} src={item.image} alt="image" />
                                        <button className={styles.btn_save}><img src={item.isSaved?'/assets/images/discover/icon_save_active.svg':'/assets/images/discover/icon_save.svg'} alt="save" /></button>
                                        <div className={styles.cate}>{item.cate}</div>
                                    </div>
                                    <div className={styles.box_info}>
                                        <p className={styles.sub_title}>Available for Readings Now</p>
                                        <p className={styles.title}>Top Energy Healers</p>
                                        <div className={styles.box_popular}>
                                            <div className={styles.popular}>
                                                <img src="/assets/images/discover/ArrowUp.svg" alt="popular" />
                                                <span className={styles.text}>Popular</span>
                                            </div>
                                            <div className={styles.line}></div>
                                            <div className={styles.box_consul}>
                                                <p className={styles.number}>{item.visit}</p>
                                                <p className={styles.text}>visits</p>
                                            </div>
                                        </div>
                                        <div className={styles.box_profile}>
                                            <div className={styles.box_avatar}>
                                                <img src={item.avatar} alt={item.name} />
                                            </div>
                                            <div className={styles.box_name}>
                                                <p className={styles.name}>{item.name}</p>
                                                <div className={styles.box_star}>
                                                    <Star value={Number(item.star)}/>
                                                    <span className={styles.total_rate}>({item.totalRate})</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div className={styles.box_tag}>
                                            {
                                                item.listTag.map((itemTag:string,i:number)=>(
                                                    
                                                    i<item.listTag.length-1?(
                                                        <>
                                                        <div className={styles.item_tag}>{itemTag}</div>
                                                        <div className={styles.rounder}></div>
                                                        </>
                                                    
                                                    ):(
                                                        <div className={styles.item_tag}>{itemTag}</div>
                                                    )
                                                ))
                                            }
                                        </div>
                                        <div className={styles.box_price}>
                                            <div className={styles.price}>
                                                <p className={styles.numb}>${item.price}/</p>
                                                <p className={styles.text}>hr</p>
                                            </div>
                                            <a className={styles.connect} href="#">Connect</a>
                                        </div>
                                    </div>
                                </a>
                            ))
                        }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
  );
};

export default Main;
