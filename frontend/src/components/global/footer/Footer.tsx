import React from "react";
import "../../../styles/global/footer.css";
const Footer = () => {
  const socialMedia = [
    { src: "/assets/images/global/footer/InstagramLogo.svg", alt: "Instagram" },
    { src: "/assets/images/global/footer/FacebookLogo.svg", alt: "Facebook" },
    { src: "/assets/images/global/footer/TiktokLogo.svg", alt: "Tiktok" },
    { src: "/assets/images/global/footer/LinkedinLogo.svg", alt: "Linkedin" },
    { src: "/assets/images/global/footer/XLogo.svg", alt: "Twitter (X)" },
    { src: "/assets/images/global/footer/YoutubeLogo.svg", alt: "YouTube" },
  ];
  return (
    <div className="container_footer">
      <div className="container_footer_content">
        <div className="container_logo_follow">
          <img
            src="/assets/images/global/logo.png"
            alt="Logo"
            className="img_logo_footer"
          />
          <div className="container_follow">
            <p className="text_follow">Follow Us On:</p>
            <div className="container_follow_items">
              {socialMedia.map((item, index) => (
                <div className="follow_items" key={index}>
                  <img src={item.src} alt={item.alt} className="icon_footer" />
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="container_Other_subscribe">
          <div className="container_Other_footer">
            <div className="container_Other_items">
              <p className="text_title_footer">Support Center</p>
              <div className="Other_items">
                <p className="text_items_footer">Resources</p>
                <p className="text_items_footer">Help & Support</p>
                <p className="text_items_footer">FAQ</p>
              </div>
            </div>
            <div className="container_Other_items">
              <p className="text_title_footer items_mobile">For Coaches</p>
              <div className="Other_items">
                <p className="text_items_footer">Become a Coach</p>
                <p className="text_items_footer">Coach Resources</p>
                <p className="text_items_footer">Coach Community</p>
              </div>
            </div>

            <div className="container_Other_items items_mobile">
              <p className="text_title_footer">Blog</p>
              <div className="Other_items">
                <p className="text_items_footer">Success Stories</p>
                <p className="text_items_footer">High Vibe Living</p>
                <p className="text_items_footer">Free Tools</p>
              </div>
            </div>
            <div className="container_Other_items items_mobile">
              <p className="text_title_footer">Hivi Quest</p>
              <div className="Other_items">
                <p className="text_items_footer">About Us</p>
                <p className="text_items_footer">Careers</p>
                <p className="text_items_footer">Investor Relations</p>
              </div>
            </div>
          </div>
          <div className="container_subscribe_footer">
            <p className="text_title_subscribe_footer">Subscribe Newsletter</p>
            <div className="container_input_email">
              <input
                type="email"
                placeholder="Your Email"
                className="input_email"
              />
              <div className="btn_subscribe_footer">
                <img src="/assets/images/global/footer/PaperPlaneTilt.svg" />
              </div>
            </div>
            <p className="text_hint_footer">
              Please sign up to follow the latest news and events from us, we
              promise not to spam your inbox.
            </p>
          </div>
        </div>
        <div className="container_term_policy_footer">
          <p className="text_hint_footer text_mobile">
            © 2025 <span className="text_hint_branding text_mobile">HiVi</span>{" "}
            ® Inc.
          </p>
          <div className="footer_term_policy">
            <p className="text_hint_footer text_mobile">Terms of Service</p>
            <div className="line_height"></div>
            <p className="text_hint_footer text_mobile">Privacy Policy</p>
            <div className="line_height"></div>
            <p className="text_hint_footer text_mobile">Cookie Settings</p>
            <div className="line_height"></div>
            <p className="text_hint_footer text_mobile">Accessibility</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
