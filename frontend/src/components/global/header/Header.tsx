import React, { useState, useEffect } from "react";
import "../../../styles/global/header.css";

const Header = () => {
  const [isOpen, setIsOpen] = useState(false);

  // Toggle menu
  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  // Ẩn menu khi bấm bên ngoài
  useEffect(() => {
    const handleClickOutside = (event:any) => {
      if (
        isOpen &&
        !event.target.closest(".menu-container") &&
        !event.target.closest(".container_menubar_mobile")
      ) {
        setIsOpen(false);
      }
    };
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <>
      <div
        className="container_header"
        style={{
          position: isOpen ? "fixed" : "static",
        }}
      >
        <div className="container_menubar_header">
          <a href="/">
            <img src="/assets/images/global/logo.png" alt="Logo" />
          </a>
          <div className="menubar_header">
            <a className="btn_header" href="/">
              <p className="text_header">Homepage</p>
            </a>
            <a className="item_menubar_header btn_header" href="/discover">
              <p className="text_header">Discover</p>
              <img
                src="/assets/images/global/CaretDown.svg"
                className="icon_down"
              />
            </a>
            <a className="item_menubar_header btn_header" href="/events">
              <p className="text_header">Event</p>
              <img
                src="/assets/images/global/CaretDown.svg"
                className="icon_down"
              />
            </a>
          </div>
        </div>
        <div className="container_login_signup">
          <div className="btn_header">
            <p className="text_login_signup">Join as a Coach</p>
          </div>
          <div className="btn_header">
            <p className="text_login_signup">Sign In</p>
          </div>
          <div className="btn_signup btn_header">
            <p className="text_signup">Signup</p>
          </div>
        </div>
        <div className="container_menubar_mobile">
          <img
            src={
              isOpen
                ? "/assets/images/global/X.svg"
                : "/assets/images/global/menu.svg"
            }
            onClick={toggleMenu}
          />
        </div>
        {/* Menu Sidebar */}
        <div className={`menu-container ${isOpen ? "open" : ""}`}>
          <ul>
            <li>
              <a href="/" className="text_sidebarHeader">
                Homepage
              </a>
            </li>
            <div className="line_width_header"></div>
            <li>
              <a href="/discover" className="text_sidebarHeader">
                Discover
              </a>
            </li>
            <div className="line_width_header"></div>
            <li>
              <a href="/events" className="text_sidebarHeader">
                Events
              </a>
            </li>
            <div className="line_width_header"></div>
            <li>
              <a href="/login" className="text_sidebarHeader">
                Join as a Seer
              </a>
            </li>
          </ul>
          <div className="container_login_signup_sidebar">
            <div className="container_btn_sidebar">
              <div className="btn_signIn text_ButtonSmall">Sign In</div>
              <div
                className="btn_singUp text_ButtonSmall"
                style={{ color: "rgba(255, 255, 255, 1)" }}
              >
                Sign Up
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="header_fixed"
        style={{
          height: isOpen ? "70px" : "0px",
        }}
      ></div>
    </>
  );
};

export default Header;
