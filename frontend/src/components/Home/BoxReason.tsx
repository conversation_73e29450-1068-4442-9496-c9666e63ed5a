import React from "react";
import styles from "../../styles/home/<USER>";
const BoxReason = () => {
  return (
    <div className={styles.box_reason}>
        <div className={styles.container_reason}>
            <div className={styles.head_reason}>
                <p className={styles.sub_title}>Why Choose Us</p>
                <p className={styles.title}>Why HiVi exist ?</p>
            </div>
            <div className={styles.box_img}>
                <img src="/assets/images/home/<USER>" alt="why HiVi exist?" />
            </div>
            <div className={styles.list_reason}>
                <div className={styles.item_reason}>
                    <p className={styles.head_item}>Reviving ancient wisdom 🌿 </p>
                    <p className={styles.content}>Our ancestors had a profound connection to nature and developed timeless tools to guide us on our human journey. By honoring these teachings, we can create a better future for generations to come, grounded in wisdom and respect for the world around us.</p>
                </div>
                <div className={styles.item_reason}>
                    <p className={styles.head_item}>Healing Beyond Science ✨</p>
                    <p className={styles.content}>Healing is deeper than what modern science understands. HiVi explores holistic approaches—energy healing, hypnosis, sound baths, and more—to help regulate your nervous system and support your overall well-being. We believe true healing integrates mind, body, and spirit.</p>
                </div>
                <div className={styles.item_reason}>
                    <p className={styles.head_item}>Integrity and Transparency in Spiritual Practices 🔍</p>
                    <p className={styles.content}>In today's world, finding authentic spiritual guidance can be challenging. HiVi brings integrity and transparency to the forefront by providing a platform where seekers can explore coaches' past work and reviews, ensuring a safe and informed journey toward healing and growth.</p>
                </div>
            </div>
            
        </div>
        
    </div>
  );
};

export default BoxReason;
