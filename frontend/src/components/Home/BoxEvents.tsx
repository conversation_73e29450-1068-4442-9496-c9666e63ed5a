import React from "react";
import styles from "../../styles/home/<USER>";
const BoxEvents = () => {
    let listEvent = [
        {
            img:'/assets/images/home/<USER>',
            name: 'Facing Relationship Triggers & Shadow Aspects',
            cat: 'Shadow Work',
            day: '08',
            month: 'DEC',
            time: '5Am - 7:00Am',
            addr:'101 E 129th St, East Chicago, IN 46312, US'
        },
        {
            img:'/assets/images/home/<USER>',
            name: 'BreathWork for Anxiety Relief',
            cat: 'Anxiety, Depression and Stress Management',
            day: '08',
            month: 'DEC',
            time: '5Am - 7:00Am',
            addr:'101 E 129th St, East Chicago, IN 46312, US'
        },
        {
            img:'/assets/images/home/<USER>',
            name: 'E<PERSON> Tapping for Emotional Healing',
            cat: 'Healing Techniques',
            day: '08',
            month: 'DEC',
            time: '5Am - 7:00Am',
            addr:'101 E 129th St, East Chicago, IN 46312, US'
        }
        
    ];
  return (
    
    <div className={styles.box_events}>
        <div className={styles.container_box_events}>
            <div className={styles.box_text}>
                <p className={styles.sub_title}>Upcoming Events</p>
                <p className={styles.title}>Exciting Events Ahead</p>
            </div>
            <div className={styles.box_list_event}>
                <div className={styles.list_event}>
                    {
                    listEvent.map((item:any,i:number)=>(
                        <a href="/events" key={i} className={styles.item_event}>
                            <div className={styles.box_img}>
                                <img src="/assets/images/home/<USER>" alt="event" />
                                <div className={styles.date_sticker}>
                                    <p className={styles.sticker_day}>16</p>
                                    <p className={styles.sticker_month}>DEC</p>
                                </div>
                            </div>
                            <div className={styles.box_info}>
                                <div className={styles.cat}>Healing Techniques</div>
                                <div className={styles.name}>EFT Tapping for Emotional Healing</div>
                                <div className={styles.list_info}>
                                    <div className={styles.row_info}>
                                        <img src="/assets/images/home/<USER>" alt="time" />
                                        <p>08 DEC: 5Am - 7:00Am</p>
                                    </div>
                                    {/* <div className={styles.row_info}>
                                        <img src="/assets/images/home/<USER>" alt="addr" />
                                        <p>101 E 129th St, East Chicago, IN 46312, US</p>
                                    </div> */}
                                </div>
                            </div>
                        </a>
                    )) 
                    }
                    
                </div>
            </div>
            
            <a className={styles.btn_load_more}>Load More</a>
        </div>
    </div>
  );
};

export default BoxEvents;
