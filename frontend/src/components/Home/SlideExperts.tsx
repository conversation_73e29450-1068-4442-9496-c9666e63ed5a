import React from "react";
import styles from "../../styles/home/<USER>";
const SlideExperts = () => {
    let listService = [
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },{
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        },
        {
            bg: '/assets/images/home/<USER>',
            name: 'Energy & Frequency Healers',
            description: 'Shadow work, EFT tapping, inner child healing, trauma-informed coaching.'
        }
    ]
  return (
    <div className={styles.slide_experts}>
        <div className={styles.box_text}>
            <p className={styles.text_des}>Our Services</p>
            <p className={styles.text_head}>Browse Experts</p>
        </div>
        <div className={styles.container_slide}>
            <button className={styles.btn_left}>
                <img src="/assets/images/home/<USER>" alt="left" />
            </button>
            <div className={styles.container_list_service}>
                <div className={styles.list_service}>
                    {
                        listService.map((item:any,i:number)=>(
                            <div key={i} className={styles.item_service}>
                                <div className={styles.bg_service} style={{background:`url('${item.bg}')`,backgroundSize:'cover',backgroundPosition:'center'}}>
                                    {/* <img src={item.bg} alt={item.name} /> */}
                                </div>
                                <div className={styles.text_service}>
                                    <p className={styles.name_service}>{item.name}</p>
                                    <p className={styles.desc_service}>{item.description}</p>
                                </div>
                            </div>
                        ))
                    }
                    
                </div>
            </div>
            <button className={styles.btn_right}>
                <img src="/assets/images/home/<USER>" alt="right" />
            </button>
        </div>
        <div className={styles.dots}>
            {
                listService.map((item:any,i:number)=>(
                    <label key={i} className={styles.item}>
                        <input type="radio" name="radio" value={i}/>
                        <div className={styles.checkmark}></div>
                    </label>
                ))
            }
            
        </div>
    </div>
  );
};

export default SlideExperts;
