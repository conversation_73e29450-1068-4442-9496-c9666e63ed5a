import React from "react";
import styles from "../../styles/home/<USER>";

import Star from "../Common/Star"

const BoxReview = () => {
    
  return (
    <div className={styles.box_review}>
        <div className={styles.container_review}>
            <div className={styles.main_review}>
                <div className={styles.img_profile}>
                    <img src="/assets/images/home/<USER>" alt="profile" />
                </div>
                <div className={styles.detail_review}>
                    <div className={styles.head_review}>
                        <p className={styles.sub_title}>Client Testimonials</p>
                        <p className={styles.title}>What seekers are saying</p>
                        <p className={styles.content}>Hear from souls who have transformed their spiritual journeys.</p>
                    </div>
                    <div className={styles.content_review}>
                        <Star value={4.8}/>
                        <p className={styles.text}>"Hi<PERSON><PERSON> has transformed my spiritual journey. The connection with healers and coaches has empowered me to discover new facets of my soul. I am constantly amazed by the genuine support and profound insights I receive, making this an irreplaceable platform for my personal growth."</p>
                        <div className={styles.box_profile}>
                            <div className={styles.content_profile}>
                                <p className={styles.name}><PERSON> <PERSON></p>
                                <p className={styles.position}>CEO Digital Avitex</p>
                            </div>
                            <a href="/" className={styles.read_more}>
                                Read More Stories
                                <img src="/assets/images/home/<USER>" alt="read more" />
                            </a>
                        </div>
                        <div className={styles.dots}>
                            <label className={styles.item}>
                                <input type="radio" name="radio"/>
                                <div className={styles.checkmark}></div>
                            </label>
                            <label className={styles.item}>
                                <input type="radio" name="radio" defaultChecked/>
                                <div className={styles.checkmark}></div>
                            </label>
                            <label className={styles.item}>
                                <input type="radio" name="radio"/>
                                <div className={styles.checkmark}></div>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div className={styles.parameter_review}>
                <div className={styles.item_parameter}>
                    <div className={styles.title}>20 years</div>
                    <div className={styles.text}>Years of Experience</div>
                </div>
                <div className={styles.item_parameter}>
                    <div className={styles.title}>1.8k</div>
                    <div className={styles.text}>Happy customers</div>
                </div>
                <div className={styles.item_parameter}>
                    <div className={styles.title}>46</div>
                    <div className={styles.text}>Therapy Sessions</div>
                </div>
                <div className={styles.item_parameter}>
                    <div className={styles.title}>15</div>
                    <div className={styles.text}>Certifications/Awards</div>
                </div>
            </div>
        </div>
    </div>
    
  );
};

export default BoxReview;
