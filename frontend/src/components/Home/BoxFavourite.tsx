import React from "react";
import styles from "../../styles/home/<USER>";
import { m } from "framer-motion";
const BoxFavourite = () => {
    let listFavourite = [
        {
            title:'Connecting Souls: Transformative Healing Through Spiritual Practices',
            content:'Discover the power of spiritual practices in transforming your life...',
            image:'/assets/images/home/<USER>',
            url:'/'
        },
        {
            title:'Astrological Insights: How Chart Readings Illuminate Your Path',
            content:`Explore how astrological chart readings offer profound insights into your life's direction...`,
            image:'/assets/images/home/<USER>',
            url:'/'
        },
        {
            title:'Harnessing Inner Peace: The Role of Spiritual Guides in Modern Life',
            content:'Discover how spiritual guides can help you harness inner peace in our hectic modern lives.',
            image:'/assets/images/home/<USER>',
            url:'/'
        }
    ]
  return (
    <div className={styles.box_favourite}>
        <div className={styles.container_favourite}>
            <div className={styles.head_favourite}>
                <p className={styles.sub_title}>Favourite Topics</p>
                <p className={styles.title}>Latest blog articles</p>
                <p className={styles.content}>Your go-to source for mental health insights, tools, and advice.</p>
            </div>
            <div className={styles.box_list_favourite}>
                <div className={styles.list_favourite}>
                    {listFavourite.map((item:any,i:number)=>(
                        <div key={i} className={styles.item_favourite}>
                            <div className={styles.box_img}>
                                <img src={item.image} alt={item.title} />
                            </div>
                            <div className={styles.box_content}>
                                <p className={styles.title}>{item.title}</p>
                                <p className={styles.content}>{item.content}</p>
                            </div>
                            <a href={item.url} className={styles.learn_more}>Learn More <img src="/assets/images/home/<USER>" alt="learn more" /></a>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    </div>
    
  );
};

export default BoxFavourite;
