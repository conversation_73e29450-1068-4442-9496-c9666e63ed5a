@import "../variable.css";
div {
  -webkit-tap-highlight-color: transparent;
  /* Ẩn hiệu ứng trên trình duyệt WebKit */
  tap-highlight-color: transparent;
  /* Ẩn trên các trình duyệt khác */
}

::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* Ẩn thanh cuộn trên Firefox */

html {
  scrollbar-width: none;
}

body {
  overflow: auto;
}

.container_content {
  width: 100%;
  max-width: 1300px;
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
  /* justify-content: center; */
}
.container_UpcomingEvents {
  width: 100%;
  max-width: 1300px;
  margin: 40px auto;
  display: flex;
  flex-direction: column;
  gap: 40px;
}
.item_events {
  max-width: 410px;
  min-width: 350px;
  width: 33%;
  overflow: hidden;
  border-radius: 12px;
  background-color: var(--Surface);
}

.container_img_item {
  overflow: hidden;
  position: relative;
}

.overlay {
  position: absolute;
  left: 24px;
  bottom: 18px;
  width: 80px;
  /* <PERSON>i<PERSON><PERSON> chỉnh kích thước */
  height: 80px;
  background-color: var(--On-Surface);
  /* <PERSON><PERSON><PERSON> n<PERSON> (có thể thay đ<PERSON>i) */
  border-radius: 8px;
  /* Bo góc */
  backdrop-filter: blur(12px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.text_day {
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: 0%;
  text-align: center;
  color: var(--White);
  margin: 0;
}

.text_month {
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  letter-spacing: 0%;
  text-align: center;
  color: var(--White);
  margin: 0;
}

.icon {
  width: 20px;
  height: 20px;
}

.container_img_item,
.img_item_events {
  width: 100%;
  height: 307px;
}

.item_content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 24px;
}
.text_titleEvent {
  font-family: Roboto;
  font-weight: 500;
  font-size: 44px;
  line-height: 52px;
  letter-spacing: 0%;
  vertical-align: middle;
  text-transform: capitalize;
  color: var(--On-Surface);
}
.text_title_item {
  margin: 0;
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  color: var(--On-Surface);
}

.item_information {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.information {
  display: flex;
  gap: 8px;
}

.text_information {
  margin: 0;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0%;
  color: var(--Secondary);
}

.container_btn_load_more {
  padding-bottom: 20px;
  width: 100%;
  display: flex;
  /* justify-content: center; */
  align-items: center;
}

.btn_load_more {
  padding: 0px 28px;
  border-radius: 40px;
  background-color: var(--Primary);
  height: 52px;
  cursor: pointer;
  user-select: none;
  transition: transform 0.3s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn_load_more:active {
  transform: translateY(-2px);
}

.text_load_more {
  margin: 0;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0%;
  color: var(--White);
}

.container_responsive {
  padding: 0 10px;
  width: 100%;
}
.container_pastEvents {
  width: 100%;
  padding-bottom: 20px;
  overflow-x: auto !important;
  display: flex;
  gap: 30px;
}
.item_past_events {
  width: 300px;
  min-width: 300px;
  overflow: hidden;
  border-radius: 12px;
  background-color: var(--Surface);
}
.container_img_past_item {
  overflow: hidden;
  position: relative;
}
.img_item_past_events,
.container_img_past_item {
  width: 100%;
  height: 225px;
}
.item_past_events_content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 20px 16px;
}
.overlay_past_events {
  position: absolute;
  left: 24px;
  bottom: 18px;
  width: 52px;
  height: 52px;
  background-color: var(--On-Surface);
  border-radius: 8px;
  backdrop-filter: blur(12px);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.text_day_past_events {
  font-family: Roboto;
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: bottom;
  color: var(--White);
}
.text_month_past_events {
  font-family: Archivo;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0%;
  text-align: center;
  vertical-align: bottom;
  color: var(--White);
}
@media screen and (max-width: 1380px) {
  .container_UpcomingEvents {
    padding: 0 25px;
  }
  .item_events {
    width: 31%;
  }
}
@media screen and (max-width: 1172px) {
  .item_events {
    width: 30%;
  }
}
@media screen and (max-width: 1160px) {
  .item_events {
    width: 45%;
  }
  .container_UpcomingEvents {
    padding: 0 40px;
  }
}
@media screen and (max-width: 830px) {
  /* .container_content {
    padding: 0 10px;
  } */
  .container_UpcomingEvents {
    padding: 0 25px;
  }
}
@media screen and (max-width: 780px) {
  .item_events {
    width: 100%;
  }
  .container_UpcomingEvents {
    padding: 0 40px;
  }
}
