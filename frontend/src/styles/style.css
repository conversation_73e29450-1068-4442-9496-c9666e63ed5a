/* Purple Range Slider */

* {
    font-family: Archivo;
}

::-webkit-scrollbar {
    width: 4px;
    background-color: transparent;
    height: 5px;
}

::-webkit-scrollbar-thumb {
    background: #cacef2;
    border-radius: 4px;
    height: 5px;
}

::-webkit-scrollbar-track {
    background-color: transparent;
}

#range-slider-purple {
    height: 4px;
}

#range-slider-purple .range-slider__range {
    background: var(--Primary);
    transition: height 0.3s;
}

#range-slider-purple .range-slider__thumb {
    width: 16px;
    height: 16px;
    background: var(--White);
    border: 3px solid var(--Primary);
    transition: transform 0.3s;
}