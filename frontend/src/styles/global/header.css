@import "../variable.css";
.container_header {
  height: 100px;
  background-color: var(--White);
  border-bottom: 1px solid var(--Outline);
  padding: 24px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.container_menubar_header {
  display: flex;
  align-items: center;
  gap: 135px;
}

.menubar_header {
  display: flex;
  gap: 40px;
}

.item_menubar_header {
  display: flex;
  gap: 8px;
  align-items: center;
}

.icon_down {
  width: 16px;
  height: 18px;
}

.text_header {
  font-weight: 500;
  font-size: 18px;
  line-height: 28px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.btn_header:hover .text_header {
  border-bottom: 2px solid var(--On-Surface);
}

.container_login_signup {
  display: flex;
  align-items: center;
  gap: 28px;
}

.text_login_signup {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  color: var(--On-Surface);
  white-space: nowrap;
}

.btn_signup {
  background-color: var(--Primary);
  color: var(--White);
  border: none;
  padding: 16px 40px;
  border-radius: 40px;
  cursor: pointer;
}

.text_signup {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  color: var(--White);
  margin: 0;
}

.btn_header,
.container_menubar_mobile {
  cursor: pointer;
  user-select: none;
  transition: transform 0.1s ease-in-out;
}

.btn_header:active {
  transform: translateY(2px);
}

.container_menubar_mobile {
  position: relative;
  display: none;
  cursor: pointer;
}

/* Overlay (Lớp nền đen) */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(0, 0, 0, 0.5);
  opacity: 1;
  z-index: 5;
}

/* Menu Sidebar */
.menu-container {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 70px;
  right: -100%; /* Ẩn menu ngoài màn hình */
  width: 100%;
  height: 100vh;
  background: white;
  /* box-shadow: -2px 0px 10px rgba(0, 0, 0, 0.2); */
  transition: right 0.3s ease-in-out;
  z-index: 10;
}

.menu-container.open {
  right: 0;
}

/* Menu Items */
.menu-container ul {
  list-style: none;
  padding: 20px;
}

.menu-container li {
  padding: 20px 0;
}

.menu-container li a {
  text-decoration: none;
  color: black;
  display: block;
}
.header_fixed {
  display: none;
}
.line_width_header {
  width: 100%;
  background-color: var(--Outline);
  height: 1px;
}
.text_sidebarHeader {
  font-family: Archivo;
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}
.container_login_signup_sidebar {
  bottom: 80px;
  position: absolute;
  display: flex;
  width: 100%;
  padding: 20px;
}
.container_btn_sidebar {
  width: 100%;
  display: flex;
  gap: 8px;
}
.btn_signIn,
.btn_singUp {
  cursor: pointer;
  user-select: none;
  width: 48%;
  height: 44px;
  padding: 16px 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.1s ease-in-out;
  border-radius: 40px;
}
.btn_signIn {
  border: 1px solid var(--Outline);
}
.btn_singUp {
  background-color: var(--Primary);
}
.btn_signIn:active,
.btn_singUp:active {
  transform: scale(0.95);
  transform: translateY(-1px);
}
@media screen and (max-width: 1060px) {
  .container_menubar_header {
    gap: 10px;
  }
}

@media screen and (max-width: 900px) {
  .menubar_header {
    gap: 15px;
  }
}

@media screen and (max-width: 880px) {
  .menubar_header {
    display: none;
  }
  .container_login_signup {
    display: none;
  }
  .container_menubar_mobile {
    display: block;
  }
  .container_header {
    z-index: 10;
    width: 100%;
    padding: 16px;
    height: 70px;
  }
  .header_fixed {
    display: flex;
  }
}
