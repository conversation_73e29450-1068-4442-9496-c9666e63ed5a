.banner {
    width: 100%;
    display: flex;
    justify-content: center;
    background: linear-gradient(90.97deg, rgba(105, 58, 240, 0.06) 0.08%, rgba(242, 62, 170, 0.06) 100%);
    padding: 20px 30px 57px 30px;
}

.container_banner {
    width: 1290px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
}

.main_banner {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.banner_text .head_text {
    font-weight: 500;
    font-size: 56px;
    line-height: 68px;
}

.banner_text .text_banner {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

@media screen and (max-width: 700px) {
    .banner {
        padding: 12px 20px 0px 20px;
    }
    .container_banner {
        gap: 20px;
    }
    .main_banner {
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
    }
    .banner_text .head_text {
        font-size: 32px;
        line-height: 40px;
    }
}