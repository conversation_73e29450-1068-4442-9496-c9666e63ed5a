.item_filter .item {
    position: relative;
    display: flex;
    flex-direction: row-reverse;
    justify-content: left;
    align-items: center;
    gap: 10px;
}

.item_filter .item .checkmark {
    width: 16px;
    height: 16px;
    border: 1px solid #191820;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.item_filter .item input {
    display: none;
}

.item_filter .item input:checked~.checkmark::after {
    content: '';
    width: 10px;
    height: 10px;
    position: absolute;
    background: #191820;
    border-radius: 50%;
    top: 2px;
    left: 2px;
}

.main_discover {
    width: 100%;
    display: flex;
    justify-content: center;
    padding: 80px 30px;
}

.container_discover {
    width: 1290px;
    max-width: 100%;
    display: flex;
    justify-content: space-between;
}

.box_filter {
    width: 270px;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.container_filter {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.container_filter .head_filter {
    font-weight: 500;
    font-size: 18px;
    line-height: 28px;
    color: var(--On-Surface);
}

.item_filter {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.item_filter .item {
    padding: 5px 0px 5px 2px;
}

.item_filter .item .name_item {
    display: flex;
    gap: 10px;
    justify-content: space-between;
    width: calc(100% - 26px);
}

.item_filter .item .name_item p {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    color: var(--On-Surface);
}

.item_filter .ranger {
    padding-top: 10px;
    padding-bottom: 10px;
}

.value_ranger {
    display: flex;
    justify-content: space-between;
}

.value_ranger .item_value {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.item_value .text_value {
    font-family: Rethink Sans;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
}

.item_value .show_value {
    width: 115px;
    padding: 7px 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    border: 1px solid var(--Outline);
}

.show_value .input_value {
    width: 100%;
}

.box_main {
    width: calc(100% - 330px);
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.box_main .box_search {
    display: flex;
    gap: 12px;
}

.box_search .container_item_select {
    width: calc((100% - 169px) / 2);
    position: relative;
    display: flex;
    align-items: center;
}

.box_search .item_select {
    width: 100%;
}

.box_search .select_location::before {
    content: 'Location';
    position: absolute;
    top: 7px;
    left: 12px;
    color: var(--Secondary);
    z-index: 9;
}

.box_search .container_item_select .icon {
    position: absolute;
    right: 8px;
}

.box_search .select_service::before {
    content: 'Service Type';
    position: absolute;
    top: 7px;
    left: 12px;
    color: var(--Secondary);
    z-index: 9;
}

.box_search .btn_search {
    width: 133px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    border: none;
    background: var(--Primary);
    padding: 18px 24px;
    border-radius: 12px;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: var(--White);
}

.box_main .box_content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.box_main .headcontent {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.box_main .headcontent .text {
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    color: var(--On-Surface);
}

.box_main .headcontent .btn_filter {
    display: flex;
    gap: 4px;
    justify-content: center;
    align-items: center;
    background: var(--White);
    border-radius: 4px;
    border: 1px solid var(--Outline);
    padding: 8px;
}

.box_list_featured {
    width: 100%;
    overflow: auto;
    padding-bottom: 20px;
}

.list_featured {
    display: flex;
    flex-direction: column;
    gap: 30px;
    flex-wrap: wrap;
    height: 924px;
}

.list_featured .item_featured {
    width: 300px;
    height: 447px;
    border-radius: 12px;
    border: 1px solid var(--Outline);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.box_content .box_img {
    position: relative;
    overflow: hidden;
}

.item_featured .box_img {
    width: 100%;
    height: 200px;
}

.item_featured .box_img img {
    min-height: 100%;
}

.box_content .box_img .btn_save {
    position: absolute;
    border-radius: 8px;
    border: none;
    background: var(--White);
    padding: 8px;
}

.item_featured .box_img .btn_save {
    top: 10px;
    right: 12px;
}

.box_content .box_img .cate {
    position: absolute;
    background: var(--White);
    border-radius: 4px;
    padding: 2px 8px;
    font-weight: 500;
    font-size: 12px;
    line-height: 20px;
    color: var(--On-Surface);
}

.item_featured .box_img .cate {
    left: 8px;
    bottom: 8px;
}

.box_content .box_info {
    display: flex;
    flex-direction: column;
}

.item_featured .box_info {
    padding: 16px;
    height: calc(100% - 200px);
}

.box_content .box_info .sub_title {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: var(--Secondary);
}

.box_content .box_info .title {
    padding-top: 4px;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: var(--On-Surface);
}

.box_content .box_info .box_popular {
    padding-top: 8px;
    display: flex;
    gap: 12px;
    align-items: center;
}

.box_content .box_info .box_popular .popular {
    display: flex;
    align-items: center;
    gap: 3px;
}

.box_content .box_info .box_popular .text {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--Secondary);
}

.item_featured .box_info .box_popular .line {
    width: 1px;
    height: 100%;
    background: var(--Secondary);
}

.box_content .box_info .box_popular .box_consul {
    display: flex;
    align-items: center;
    gap: 4px;
}

.box_popular .box_consul .number {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    color: var(--On-Surface);
}

.box_content .box_info .box_profile {
    padding-top: 16px;
    padding-bottom: 16px;
    display: flex;
    gap: 16px;
}

.box_content .box_profile .box_avatar {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    overflow: hidden;
}

.box_content .box_profile .box_avatar img {
    max-width: 100%;
    max-height: 100%;
    min-width: 100%;
    min-height: 100%;
}

.box_content .box_profile .box_name {
    display: flex;
    flex-direction: column;
}

.box_content .box_profile .box_name .name {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: var(--On-Surface);
}

.box_content .box_profile .box_name .box_star {
    display: flex;
    gap: 4px;
}

.box_content .box_profile .box_name .box_star .total_rate {
    font-weight: 400;
    font-size: 12px;
    line-height: 20px;
    color: var(--Secondary);
}

.box_content .box_info .box_price {
    padding-top: 16px;
    display: flex;
    align-items: center;
}

.item_featured .box_info .box_price {
    justify-content: space-between;
}

.box_content .box_price .connect {
    border-radius: 8px;
    border: 1px solid var(--Primary);
    padding: 9px 20px;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
}

.box_content .box_price .price {
    display: flex;
}

.box_content .box_price .price .numb {
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
}

.box_content .box_price .text {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.box_list_available {
    width: 100%;
}

.list_available {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.item_available {
    width: 100%;
    height: 280px;
    display: flex;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid var(--Outline);
}

.item_available .box_img {
    width: 43.75%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.item_available .box_img .image {
    min-width: 100%;
    min-height: 100%;
    max-width: unset;
    max-height: unset;
    width: auto;
    height: auto;
    aspect-ratio: 1024/768;
    max-height: 100%;
    position: absolute;
}

.item_available .box_img .cate {
    bottom: 8px;
    left: 8px;
}

.item_available .box_img .btn_save {
    top: 20px;
    right: 20px;
}

.item_available .box_info {
    width: 56.25%;
    padding: 20px;
}

.item_available .box_info .box_tag {
    display: flex;
    gap: 12px;
    align-items: center;
}

.box_content .box_info .box_price .price {
    min-width: 98px;
}

@media screen and (max-width: 1168px) {
    .box_list_available {
        overflow: auto;
        padding-bottom: 20px;
    }
    .list_available {
        flex-direction: row;
        width: fit-content;
    }
    .list_available .item_available {
        width: 300px;
        height: 447px;
        flex-direction: column;
    }
    .list_available .item_available .box_img {
        width: 100%;
        height: 200px;
    }
    .item_available .box_img .btn_save {
        top: 10px;
        right: 12px;
    }
    .list_available .item_available .box_info {
        width: 100%;
        height: calc(100% - 200px);
    }
    .item_available .box_info .box_tag {
        display: none;
    }
    .box_content .box_info .box_price {
        justify-content: space-between;
    }
}

@media screen and (max-width: 1024px) {
    .main_discover {
        position: relative;
    }
    .box_filter {
        width: 100%;
        position: absolute;
        background: var(--White);
        z-index: 9;
        top: 225px;
        left: 0;
        padding: 20px;
    }
    .box_filter.hide {
        display: none;
    }
    .box_main {
        width: 100%;
    }
}

@media screen and (max-width: 700px) {
    .main_discover {
        padding: 40px 20px;
    }
    .box_main .box_search {
        flex-direction: column;
    }
    .box_search .item_select {
        width: 100%;
        height: 52px;
    }
    .box_search .btn_search {
        width: 100%;
        padding: 12px;
    }
    .list_featured {
        height: 447px;
    }
    .box_filter {
        top: 340px;
    }
}