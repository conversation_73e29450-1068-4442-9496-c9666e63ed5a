.signup {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
}

.error {
    color: red;
    font-size: 12px;
}

.main_signup {
    display: flex;
    flex-direction: column;
    width: 660px;
    max-width: 100%;
    border: 1px solid rgba(201, 201, 201, 0.23);
    box-shadow: rgba(202, 202, 202, 0.3) 0px 0px 8px 2px;
    padding: 30px;
    border-radius: 5px;
}

.main_signup .head {
    font-family: Raleway;
    font-size: 22px;
    font-weight: bold;
    color: rgb(0, 0, 0);
    text-align: center;
}

.box_fg {
    display: flex;
    gap: 20px;
    margin-top: 20px;
    width: 100%;
}

.box_fg .btn_login_by {
    font-family: Montserrat;
    font-size: 13px;
    width: 300px;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #000;
    font-weight: bold;
    border-style: solid;
    border-width: 1px;
    border-color: rgba(201, 201, 201, 0.47);
}

.box_line {
    align-self: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 300px;
    margin: 20px 0;
}

.box_line .line {
    width: calc((100% - 25px) / 2);
    height: 1px;
    background: rgba(202, 202, 202, 0.5);
}

.box_line .text {
    width: 25px;
    text-align: center;
    font-family: Montserrat;
    font-size: 13px;
    font-weight: 400;
}

.row_input {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.inp_full {
    width: 100%;
    height: 45px;
}

.row_input input {
    border-style: solid;
    border-width: 2px;
    border-color: rgba(201, 201, 201, 0.25);
    border-radius: 2px;
    padding: 10px;
    width: 100%;
}

.row_input .inp_haft {
    width: calc((100% - 10px) / 2);
}

.row_check.first {
    margin-top: 20px;
    margin-bottom: 5px;
}

.row_check .item {
    display: flex;
    flex-direction: row-reverse;
    gap: 10px;
    justify-content: flex-start;
}

.row_check .item p {
    width: calc(100% - 30px);
}

.row_check .item input {
    display: none;
}

.row_check .item .checkmark {
    width: 17px;
    height: 17px;
    border-radius: 2px;
    border: 2px solid rgb(17, 79, 84);
    position: relative;
    margin-top: 2px;
}

.row_check .item input:checked~.checkmark::after {
    content: '';
    position: absolute;
    transform: rotate(-45deg);
    border-left: 2px solid rgb(17, 79, 84);
    border-bottom: 2px solid rgb(17, 79, 84);
    width: 13px;
    height: 4px;
    top: -1px;
    right: -4px;
    background: #fff;
}

.btn_submit {
    width: 100%;
    margin: 20px 0px;
    width: 100%;
    height: 45px;
    border: none;
    background: rgba(64, 193, 178, 1);
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 600;
    color: rgb(255, 255, 255);
}

.box_login {
    align-self: center;
    font-family: Montserrat;
    font-size: 13px;
    font-weight: 400;
    color: #000;
    margin-bottom: 5px;
}

.box_login a {
    font-family: Montserrat;
    font-size: 13px;
    font-weight: 400;
    color: rgb(196, 148, 80);
    padding-left: 5px;
}

@media screen and (max-width: 600px) {
    .box_fg {
        flex-direction: column;
    }
    .box_fg .btn_login_by {
        width: 100%;
    }
}