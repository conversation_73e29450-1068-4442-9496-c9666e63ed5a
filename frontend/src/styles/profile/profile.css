@import "../variable.css";
.container_content_profile {
  padding: 0 20px;
}

.container_Breadcrumb {
  max-width: 1280px;
  width: 100%;
  margin: 0 auto;
  padding-top: 20px;
}

.container_description_rating_reviews {
  display: flex;
  margin-top: 40px !important;
  flex-direction: column;
  width: 100%;
  max-width: 1070px;
  margin: 0 auto;
}

.container_navbar_des_rate {
  display: flex;
  gap: 40px;
  align-items: center;
  justify-content: center;
}

.container_select,
.container_no_select {
  padding: 8px 0px;
  cursor: pointer;
  user-select: none;
}

.container_select {
  border-bottom: 4px solid var(--On-Surface);
}

.text_select {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0%;
  color: var(--On-Surface);
  white-space: nowrap;
}

.text_no_select {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0%;
  color: var(--Placeholder);
  white-space: nowrap;
}

.container_content_select {
  width: 100%;
  border: 2px solid var(--Outline);
  border-radius: 20px;
  padding: 40px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.container_content_description {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.item_content_description {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.text_description_name {
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.text_description_content {
  font-weight: 400;
  font-size: 18px;
  line-height: 30px;
  letter-spacing: 0%;
  color: var(--Secondary);
}

.container_related_products {
  max-width: 1280px;
  margin: 0px auto;
  padding: 80px 0;
  display: flex;
  flex-direction: column;
  gap: 40px;
  align-items: center;
  justify-content: center;
}

.text_related {
  font-weight: 500;
  font-size: 32px;
  line-height: 40px;
  letter-spacing: 0%;
  text-align: center;
  color: var(--On-Surface);
}

.container_lists_products {
  padding-bottom: 20px;
  width: 100%;
  display: flex;
  flex-direction: row;
  gap: 30px;
  overflow-x: auto;
}

.container_item_product {
  width: 23%;
}

@media screen and (max-width: 1180px) {
  .container_item_product {
    width: fit-content;
  }
}

@media screen and (max-width: 850px) {
  .container_content_select {
    padding: 20px;
  }
}
