@import "../variable.css";
.container_products {
  min-width: 250px;
  max-width: 300px;
  width: 300px;
  border: 1px solid var(--Outline);
  border-radius: 12px;
  overflow: hidden;
  background-color: var(--White);
  display: flex;
  flex-direction: column;
}

.container_img_products {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.img_products {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.container_tag_products {
  position: absolute;
  top: 10px;
  right: 12px;
  width: 40px;
  height: 40px;
  background-color: var(--White);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container_name_products {
  position: absolute;
  left: 8px;
  bottom: 6px;
  border-radius: 4px;
  padding: 2px 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--White);
}

.text_tags_products {
  font-weight: 500;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.container_details_products {
  display: flex;
  padding: 16px;
  flex-direction: column;
  gap: 16px;
}

.container_title_products {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.container_title_items_products {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.text_Heading6 {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.text_Caption02 {
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  letter-spacing: 0%;
  color: var(--Secondary);
}

.container_number_consultations {
  display: flex;
  gap: 12px;
  align-items: center;
}

.container_popular_products {
  display: flex;
  align-items: center;
  gap: 4px;
}

.img_16 {
  width: 16px;
  height: 16px;
}

.text_Caption01 {
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0%;
  color: var(--Secondary);
}

.line_height_Outline {
  width: 1px;
  height: 16px;
  background-color: var(--Outline);
}

.container_text_number_consultations {
  display: flex;
  align-items: center;
  gap: 4px;
}

.text_ButtonSmall {
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.container_avt_ratings {
  display: flex;
  gap: 16px;
  align-items: center;
}

.avatar_40 {
  width: 40px;
  height: 40px;
  object-fit: cover;
}

.container_name_ratings {
  display: flex;
  flex-direction: column;
}

.text_Button {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.container_number_ratings {
  display: flex;
  align-items: center;
  gap: 4px;
}

.icon_12 {
  width: 12px;
  height: 12px;
}

.container_star_ratings {
  display: flex;
  gap: 2px;
}

.container_line_width {
  height: 1px;
  background-color: var(--Outline);
}

.container_price_btnConnect {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.container_price_hour {
  display: flex;
  align-items: center;
  gap: 4px;
}

.text_Heading05 {
  font-weight: 500;
  font-size: 24px;
  line-height: 32px;
  letter-spacing: 0%;
  color: var(--On-Surface);
}

.textBody2 {
  font-weight: 400;
  font-size: 18px;
  line-height: 30px;
  letter-spacing: 0%;
  color: var(--Secondary);
}

.container_btnConnect,
.container_btnConnectTags {
  height: 40px;
  border-radius: 8px;
  padding: 8px 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  user-select: none;
  transition: transform 0.1 ease;
}

.container_btnConnect:active,
.container_btnConnectTags:active {
  transform: scale(0.98);
}

.container_btnConnect {
  border: 1px solid var(--Primary);
}

.container_btnConnectTags {
  background-color: var(--Primary);
}

.text_ButtonSmallWhite {
  font-weight: 600;
  font-size: 14px;
  line-height: 22px;
  letter-spacing: 0%;
  color: var(--White);
}
