@import "../variable.css";
.container_img_description {
    width: 100%;
    max-width: 1280px;
    margin: 0 auto;
    margin-top: 36px !important;
    display: flex;
    gap: 80px;
}

.container_list_img {
    width: 48%;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.container_img_selects {
    width: 100%;
    border-radius: 16px;
    overflow: hidden;
    height: 630px;
}

.img_select {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.container_list_img_select {
    overflow-x: auto;
    display: flex;
    flex-direction: row;
    gap: 12px;
}

.container_img_items {
    min-width: 100px;
    width: 100px;
    height: 100px;
    border-radius: 8px;
    outline: var(--Outline);
    overflow: hidden;
}

.thumbnail {
    width: 100px;
    height: 100px;
    object-fit: cover;
    cursor: pointer;
    border-radius: 5px;
    transition: all 0.2s ease-in-out;
}

.thumbnail.active {
    transform: scale(1.1);
}

.container_description_Img {
    width: 48%;
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.container_title_price {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.container_title_vote {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.container_vote_star {
    display: flex;
    gap: 8px;
}

.container_star {
    display: flex;
    align-items: center;
    gap: 2px;
}

.star {
    width: 16px;
    height: 16px;
}

.text_vote {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.text_title {
    font-weight: 500;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.container_price {
    display: flex;
    gap: 4px;
    align-items: center;
}

.text_price {
    font-weight: 500;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.text_hour {
    font-weight: 400;
    font-size: 24px;
    line-height: 32px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.text_description_img {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.container_type_color {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.container_text_type {
    display: flex;
    align-items: center;
    gap: 4px;
}

.text_type,
.text_hours {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.text_type_select,
.text_number_time {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.container_list_color {
    display: flex;
    align-items: center;
    gap: 12px;
}

.container_color,
.container_select_color {
    min-width: 40px;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background-color: var(--White);
    overflow: hidden;
    padding: 4px;
    cursor: pointer;
}

.background_Color {
    width: 48px;
    height: 48px;
    background-color: var(--White);
}

.container_select_color {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--On-Surface);
}

.container_time {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.container_btn_change_time {
    height: 48px;
    width: fit-content;
    display: flex;
    width: 180px;
    border-radius: 100px;
    border: 2px solid var(--Outline);
    padding: 0 20px;
    align-items: center;
    justify-content: space-between;
}

.icon_24 {
    cursor: pointer;
    width: 24px;
    height: 24px;
    transition: transform 0.1s ease;
}

.icon_24:active {
    transform: scale(0.9);
}

.container_btn_buy_cart {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.container_btn_add_cart {
    display: flex;
    align-items: center;
    gap: 10px;
}

.btn_add_cart {
    flex-grow: 1;
    border-radius: 99px;
    padding: 14px 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--On-Surface);
    height: 52px;
    transition: transform 0.1s ease;
    cursor: pointer;
    user-select: none;
}

.btn_add_cart:active,
.btn_items_comp_fav:active,
.container_btn_buy_items:active {
    transform: scale(0.99);
}

.text_add_cart {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--White);
}

.btn_items_comp_fav {
    min-width: 52px;
    width: 52px;
    height: 52px;
    border-radius: 99px;
    padding: 8px;
    border: 1px solid var(--Outline);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    user-select: none;
    transition: transform 0.1s ease;
}

.img_24 {
    width: 24px;
    height: 24px;
}

.container_btn_buy_items {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 52px;
    padding: 14px 40px;
    border-radius: 99px;
    background-color: var(--Primary);
    transition: transform 0.1s ease;
    cursor: pointer;
    user-select: none;
}

.text_buy {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--White);
}

@media screen and (max-width: 860px) {
    .container_img_description {
        gap: 20px;
    }
}

@media screen and (max-width: 750px) {
    .container_img_description {
        flex-direction: column;
    }
    .container_list_img,
    .container_description_Img,
    .container_btn_change_time {
        width: 100%;
    }
    .container_img_selects {
        height: 450px;
    }
}