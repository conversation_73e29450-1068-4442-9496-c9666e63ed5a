@import "../variable.css";
.container_rating_reviews {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.container_rating_btn_write {
    display: flex;
    align-items: center;
    gap: 40px;
    justify-content: space-between;
}

.container_average_point {
    width: 128px;
    min-width: 128px;
    display: flex;
    align-items: center;
    flex-direction: column;
}

.text_Heading01 {
    font-weight: 500;
    font-size: 80px;
    line-height: 88px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--On-Surface);
}

.container_star_ratings {
    display: flex;
    gap: 2px;
}

.text_bodyDefault {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--On-Surface);
}

.container_btn_write {
    width: 183px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 99px;
    border: 2px solid var(--On-Surface);
    min-width: 183px;
    transition: all 0.1s ease;
    user-select: none;
    cursor: pointer;
}

.container_btn_write:active,
.container_btn_filter_cmt:active {
    transform: scale(0.98);
}

.container_percent_rating {
    display: flex;
    flex-grow: 1;
    flex-direction: column;
    gap: 4px;
}

.container_percent_rating_mobile {
    margin-top: -20px;
    display: none;
    flex-grow: 1;
    flex-direction: column;
    gap: 4px;
}

.rating-row {
    display: flex;
    align-items: center;
    gap: 8px;
}

.img_20 {
    width: 20px;
    height: 20px;
}

.progress-bar {
    flex: 1;
    height: 10px;
    background: var(--Outline);
    position: relative;
    overflow: hidden;
}

.fill {
    height: 100%;
    background: var(--On-Surface);
    transition: width 0.3s ease-in-out;
}

.text_percent {
    width: 34px;
    font-family: Plus Jakarta Sans;
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.container_list_comment {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.container_total_filter_cmt {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.text_Heading04 {
    font-weight: 500;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.container_filter_cmt {
    display: flex;
    gap: 8px;
    align-items: center;
}

.text_bodyDefaultSecondary {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.filter-dropdown {
    position: relative;
    display: inline-block;
}

.container_btn_filter_cmt {
    display: flex;
    width: 164px;
    min-width: 164px;
    height: 48px;
    align-items: center;
    justify-content: space-between;
    padding: 11px 16px;
    border: 1px solid var(--Outline);
    border-radius: 8px;
    user-select: none;
    cursor: pointer;
    transition: all 0.1s ease;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 1px solid #ccc;
    border-radius: 5px;
    margin-top: 5px;
    box-shadow: 0px 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 10;
}

.dropdown-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s;
}

.dropdown-item:hover {
    background: #f0f0f0;
}

.container_list_cmt {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.container_item_comment {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding-bottom: 24px;
    border-bottom: 1px solid var(--Outline);
}

.container_avatar_rating_cmt {
    display: flex;
    gap: 16px;
    align-items: center;
}

.container_avatar_cmt {
    width: 52px;
    height: 52px;
    min-width: 52px;
    border-radius: 1000px;
    overflow: hidden;
}

.avatar_cmt {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.container_rating_cmt {
    display: flex;
    flex-direction: column;
}

.container_rating_user {
    display: flex;
    align-items: center;
    gap: 8px;
}

.img_12 {
    width: 12px;
    height: 12px;
}

.text_cmt {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.container_list_img_cmt {
    display: flex;
    gap: 12px;
    align-items: center;
    overflow-x: auto;
}

.container_img_cmt {
    width: 80px;
    min-width: 80px;
    height: 80px;
    border-radius: 8px;
    overflow: hidden;
}

.icon_80 {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.container_count_like {
    display: flex;
    gap: 4px;
    align-items: center;
}

.icon_20 {
    width: 20px;
    height: 20px;
    transition: transform 0.1s ease;
    cursor: pointer;
}

.icon_20:active {
    transform: scale(0.9);
}

.container_write_review {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.container_rating_star {
    display: flex;
    gap: 12px;
    align-items: center;
}

.img_40 {
    width: 40px;
    height: 40px;
}

.container_input_submit_review {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.container_input_item_review {
    display: flex;
    gap: 8px;
    flex-direction: column;
}

.text_bodyDefaultReview {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.input_review {
    width: 100%;
    height: 48px;
    padding: 11px 16px;
    border-radius: 8px;
    border: 1px solid var(--Outline);
    outline: none;
}

.input_area_review {
    width: 100%;
    padding: 11px 16px;
    border-radius: 8px;
    border: 1px solid var(--Outline);
    outline: none;
}

.container_input_name_email {
    display: flex;
    gap: 20px;
    justify-content: space-between;
}

.input_name_email {
    width: 48%;
    padding: 11px 16px;
    border-radius: 8px;
    border: 1px solid var(--Outline);
    outline: none;
}

.container_save_information {
    display: flex;
    align-items: center;
    gap: 4px;
}

.text_save_information {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 0%;
    text-align: start;
    color: var(--On-Surface);
}

.checkboxSaveInformation {
    width: 12px;
    height: 12px;
}

.btn_submit_reviews {
    min-width: 199px;
    width: 199px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 14px 40px;
    border-radius: 99px;
    background-color: var(--Primary);
    user-select: none;
    cursor: pointer;
    transition: all 0.1s ease;
}

.btn_submit_reviews:active {
    transform: scale(0.98);
}

.text_SubmitReviews {
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    text-align: center;
    color: var(--White);
    white-space: nowrap;
}

.text_totalCmt {
    font-weight: 500;
    font-size: 32px;
    line-height: 40px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.text_SortBy {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

@media screen and (max-width: 850px) {
    .container_content_select {
        padding: 20px;
    }
    .container_rating_btn_write {
        gap: 15px;
    }
}

@media screen and (max-width: 700px) {
    .container_percent_rating {
        display: none;
    }
    .container_percent_rating_mobile {
        display: flex;
    }
}

@media screen and (max-width: 560px) {
    .container_rating_star {
        flex-direction: column;
        align-items: start;
    }
    .container_input_name_email {
        flex-direction: column;
    }
    .input_name_email {
        width: 100%;
    }
    .text_totalCmt {
        font-weight: 500;
        font-size: 24px;
        line-height: 32px;
        letter-spacing: 0%;
        color: var(--On-Surface);
    }
    .container_btn_filter_cmt {
        min-width: 135px;
        width: 135px;
    }
    .text_SortBy {
        display: none;
    }
    .container_save_information {
        align-items: start;
    }
    .checkboxSaveInformation {
        margin-top: 4px;
    }
    .btn_submit_reviews {
        width: 100%;
    }
}