.slide_experts {
    display: flex;
    flex-direction: column;
    gap: 40px;
    justify-content: center;
    align-items: center;
    padding: 80px 30px;
    overflow: hidden;
}

.container_slide {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 1290px;
    position: relative;
}

.container_list_service {
    display: flex;
    align-items: center;
    overflow: hidden;
    max-width: 100%;
}

.box_text {
    display: flex;
    flex-direction: column;
    gap: 12px;
    justify-content: center;
    align-items: center;
}

.box_text .text_des {
    color: var(--On-Surface);
    font-size: 14px;
    line-height: 22px;
    font-weight: 600;
    text-transform: uppercase;
}

.box_text .text_head {
    color: var(--On-Surface);
    font-size: 44px;
    line-height: 52px;
    font-weight: 500;
}

.list_service {
    display: flex;
    gap: 30px;
    align-items: center;
    justify-items: center;
}

.item_service {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 300px;
    height: 400px;
    position: relative;
    overflow: hidden;
    border-radius: 12px;
}

.item_service .bg_service {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.item_service .bg_service img {
    width: auto;
    height: 100%;
    max-width: unset;
}

.item_service .text_service {
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    padding: 16px;
    width: 100%;
    height: 100%;
}

.item_service .text_service .name_service {
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    color: var(--White);
    font-family: 'Archivo';
}

.item_service .text_service .desc_service {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    color: var(--White);
    font-family: 'Archivo';
    display: none;
}

.item_service .text_service:hover .desc_service {
    display: block;
}

.item_service .text_service:hover .name_service {
    text-decoration: underline;
}

.dots {
    display: flex;
    gap: 2px;
}

.dots .item {
    width: 16px;
    height: 16px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dots .item .checkmark {
    width: 8px;
    height: 8px;
    border: 1px solid #191820;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dots .item input {
    display: none;
}

.dots .item input:checked~.checkmark {
    width: 16px;
    height: 16px;
}

.dots .item input:checked~.checkmark::after {
    content: '';
    width: 8px;
    height: 8px;
    position: absolute;
    background: #191820;
    border-radius: 50%;
}


/* .container_slide .box_btn {
    position: absolute;
} */

.container_slide button {
    padding: 14px;
    background: var(--White);
    border: none;
    border-radius: 50%;
    position: absolute;
}

.container_slide button.btn_left {
    left: -26px;
}

.container_slide button.btn_right {
    right: -26px;
}

@media screen and (max-width: 1366px) {
    .container_list_service {
        overflow: unset;
    }
    .container_slide {
        width: 960px;
        align-self: flex-start;
    }
    .container_slide button.btn_left {
        left: 10px;
    }
    .container_slide button.btn_right {
        right: 10px;
    }
    .dots {
        display: none;
    }
}

@media screen and (max-width: 968px) {
    .container_slide {
        width: 630px;
    }
}

@media screen and (max-width: 660px) {
    .slide_experts {
        padding: 40px 20px;
        gap: 20px;
    }
    .container_slide {
        width: 300px;
    }
    .box_text {
        gap: 4px;
    }
    .box_text .text_des {
        font-size: 12px;
        line-height: 18px;
    }
    .box_text .text_head {
        font-size: 32px;
        line-height: 40px;
    }
}