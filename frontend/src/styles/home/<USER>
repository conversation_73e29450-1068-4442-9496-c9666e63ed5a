@import "../variable.css";
.banner_home {
    width: 100%;
    display: flex;
    justify-content: left;
    align-items: center;
    padding-left: calc((100% - 1290px) / 2);
    padding-right: 80px;
    background: linear-gradient( 90.97deg, rgba(105, 58, 240, 0.06) 0.08%, rgba(242, 62, 170, 0.06) 100%);
}

.container_banner {
    width: 1920px;
    height: 660px;
}

.container_content_banner {
    max-width: 100%;
    height: 100%;
    margin: 0 auto;
}

.content_banner {
    align-items: center;
    display: flex;
    height: 100%;
    overflow: hidden;
    justify-content: flex-start;
}

.box_text_banner {
    width: 630px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.head_banner {
    font-weight: 500;
    font-size: 45px;
    line-height: 80px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.text_banner {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.content_banner .btn_banner {
    display: flex;
    align-items: center;
    gap: 20px;
}

.content_banner .btn_banner .btn_link {
    display: flex;
    gap: 4px;
    align-items: center;
    justify-content: center;
    background: var(--White);
    padding: 16px 28px;
    border-radius: 40px;
    color: var(--On-Surface);
    font-size: 16px;
    line-height: 24px;
}

.content_banner .btn_banner .btn_link.btn_primary {
    background: var(--On-Surface);
    color: var(--White);
}

.img_banner {
    width: calc(100% - 630px);
    max-width: 950px;
    height: auto;
}

@media screen and (max-width: 1280px) {
    .banner_home {
        padding-left: 40px;
        padding-right: 40px;
    }
    .box_text_banner {
        width: 530px;
    }
    .head_banner {
        font-size: 40px;
    }
    .img_banner {
        width: calc(100% - 530px);
        height: auto;
    }
}

@media screen and (max-width: 968px) {
    .container_banner {
        height: auto;
    }
    .content_banner {
        flex-direction: column-reverse;
    }
    .banner_home {
        padding: 20px 50px;
    }
    .box_text_banner {
        width: 100%;
    }
    .img_banner {
        width: 100%;
        height: auto;
        max-width: 800px;
    }
}

@media screen and (max-width: 768px) {
    .head_banner {
        font-size: 40px;
    }
}

@media screen and (max-width:500px) {
    .banner_home {
        padding: 20px;
    }
    .box_text_banner {
        gap: 8px;
    }
    .head_banner {
        font-size: 32px;
        line-height: 40px;
    }
    .text_banner {
        font-size: 16px;
        line-height: 26px;
    }
    .content_banner .btn_banner .btn_link {
        font-size: 14px;
        line-height: 22px;
        padding: 11px 28px;
    }
}