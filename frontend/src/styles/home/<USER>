.box_reason {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100px 30px;
}

.container_reason {
    width: 1290px;
}

.container_reason .box_left {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.container_reason .head_reason {
    width: calc(100% - 670px);
    float: left;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.container_reason .head_reason .sub_title {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 8%;
    color: var(--On-Surface);
}

.container_reason .head_reason .title {
    font-weight: 500;
    font-size: 44px;
    line-height: 52px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.container_reason .list_reason {
    width: calc(100% - 670px);
    float: left;
    padding-top: 40px;
    display: flex;
    flex-direction: column;
    gap: 28px;
}

.container_reason .item_reason {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.container_reason .item_reason .head_item {
    background: url('/assets/images/home/<USER>') no-repeat;
    background-size: 19.5px 19.5px;
    background-position: left 5px;
    text-indent: 25px;
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: var(--On-Surface);
}

.container_reason .item_reason .content {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.container_reason .box_img {
    width: 570px;
    float: right;
    overflow: hidden;
    border-radius: 20px;
}

@media screen and (max-width: 1366px) {
    .container_reason .box_img {
        width: 500px;
    }
    .container_reason .list_reason,
    .container_reason .head_reason {
        width: calc(100% - 600px);
    }
}

@media screen and (max-width: 1200px) {
    .container_reason .box_img {
        width: 400px;
    }
    .container_reason .list_reason,
    .container_reason .head_reason {
        width: calc(100% - 500px);
    }
}

@media screen and (max-width: 900px) {
    .box_reason {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 30px 20px;
    }
    .container_reason .box_img {
        width: 100%;
    }
    .container_reason .list_reason,
    .container_reason .head_reason {
        width: 100%;
    }
    .container_reason .list_reason {
        padding-top: 20px;
        gap: 28px;
    }
    .container_reason .head_reason {
        padding-bottom: 20px;
        gap: 4px;
    }
}

@media screen and (max-width: 500px) {
    .container_reason .head_reason .sub_title {
        font-size: 12px;
        line-height: 18px;
    }
    .container_reason .head_reason .title {
        font-size: 32px;
        line-height: 40px;
    }
    .container_reason .item_reason .head_item {
        font-size: 18px;
        line-height: 28px;
    }
    .container_reason .item_reason .content {
        font-size: 14px;
        line-height: 22px;
    }
}