.box_favourite {
    width: 100%;
    padding: 100px 30px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.container_favourite {
    width: 1290px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.container_favourite .head_favourite {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
}

.container_favourite .head_favourite .sub_title {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 8%;
    color: var(--On-Surface);
    text-align: center;
}

.container_favourite .head_favourite .title {
    font-weight: 500;
    font-size: 44px;
    line-height: 52px;
    padding-bottom: 4px;
    color: var(--On-Surface);
    text-align: center;
}

.container_favourite .head_favourite .content {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    color: var(--Secondary);
    text-align: center;
}

.box_list_favourite {
    width: 100%;
    float: left;
    overflow: auto;
    display: flex;
    justify-content: center;
}

.container_favourite .list_favourite {
    display: flex;
    gap: 30px;
    justify-content: center;
}

.container_favourite .list_favourite .item_favourite {
    width: 410px;
    height: 504px;
    border-radius: 12px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 24px;
}

.list_favourite .item_favourite .box_img {
    border-radius: 12px;
    overflow: hidden;
    width: 100%;
    height: 308px;
}

.list_favourite .item_favourite .box_img img {
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
}

.list_favourite .item_favourite .box_content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.list_favourite .item_favourite .box_content .title {
    font-weight: 500;
    font-size: 24px;
    line-height: 32px;
    color: var(--On-Surface);
}

.list_favourite .item_favourite .box_content .content {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.list_favourite .item_favourite .learn_more {
    width: 100%;
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: var(--On-Surface);
}

@media screen and (max-width: 1366px) {
    .box_favourite {
        width: 100%;
        padding: 40px 0px;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    .container_favourite .head_favourite {
        padding: 0px 16px;
        gap: 4px;
    }
    .container_favourite .head_favourite .sub_title {
        font-size: 12px;
        line-height: 18px;
    }
    .container_favourite .head_favourite .title {
        font-size: 32px;
        line-height: 40px;
        padding-bottom: 4px;
    }
    .container_favourite .head_favourite .content {
        font-size: 16px;
        line-height: 26px;
    }
    .box_list_favourite {
        padding: 0px 16px;
    }
    .container_favourite .list_favourite {
        width: 940px;
        gap: 20px;
    }
    .container_favourite .list_favourite .item_favourite {
        width: 300px;
        height: 387px;
        gap: 16px;
    }
    .list_favourite .item_favourite .box_img {
        height: 225px;
    }
    .list_favourite .item_favourite .box_content .title {
        font-size: 20px;
        line-height: 28px;
    }
    .list_favourite .item_favourite .box_content .content {
        font-size: 14px;
        line-height: 22px;
    }
    .list_favourite .item_favourite .learn_more {
        font-size: 14px;
        line-height: 22px;
    }
}

@media screen and (max-width: 970px) {
    .box_list_favourite {
        display: block;
        padding-bottom: 20px;
    }
}