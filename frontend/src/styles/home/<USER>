.box_review {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 30px;
}

.box_review .container_review {
    width: 1290px;
    max-width: 100%;
}

.container_review {
    display: flex;
    flex-direction: column;
    gap: 80px;
    background: var(--Surface);
    border-radius: 30px;
    overflow: hidden;
    padding: 40px;
}

.container_review .main_review {
    display: flex;
    padding-bottom: 40px;
    border-bottom: 1px solid #E4E4E4;
}

.container_review .main_review .img_profile {
    width: 520px;
    height: 520px;
    display: flex;
    border-radius: 20px;
    overflow: hidden;
}

.container_review .main_review .detail_review {
    width: calc(100% - 520px);
    padding: 0px 60px;
    display: flex;
    flex-direction: column;
    gap: 40px;
    justify-content: center;
}

.detail_review .head_review {
    display: flex;
    gap: 12px;
    flex-direction: column;
}

.detail_review .head_review .sub_title {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 8%;
    color: var(--On-Surface);
}

.detail_review .head_review .title {
    font-weight: 500;
    font-size: 44px;
    line-height: 52px;
    color: var(--On-Surface);
    padding-bottom: 4px;
}

.detail_review .head_review .content {
    font-weight: 400;
    font-size: 18px;
    line-height: 30px;
    color: var(--Secondary);
}

.detail_review .content_review {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.detail_review .content_review .star_rate {
    display: flex;
    align-items: center;
}

.detail_review .content_review .text {
    font-weight: 500;
    font-size: 20px;
    line-height: 32px;
    color: var(--On-Surface);
}

.detail_review .content_review .box_profile {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
}

.detail_review .content_review .box_profile .content_profile {
    display: flex;
    flex-direction: column;
    gap: 4px;
    width: 100%;
}

.box_profile .content_profile .name {
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: var(--On-Surface);
}

.box_profile .content_profile .position {
    font-weight: 400;
    font-size: 16px;
    line-height: 26px;
    letter-spacing: 0%;
    color: var(--Secondary);
}

.box_profile .read_more {
    display: flex;
    gap: 4px;
    width: 300px;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    color: var(--On-Surface);
}

.container_review .parameter_review {
    display: flex;
}

.container_review .parameter_review .item_parameter {
    width: 25%;
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.parameter_review .item_parameter .title {
    font-weight: 500;
    font-size: 44px;
    line-height: 52px;
    color: var(--On-Surface);
}

.parameter_review .item_parameter .text {
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: var(--Secondary);
}

.dots {
    display: flex;
    gap: 2px;
}

.dots .item {
    width: 16px;
    height: 16px;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dots .item .checkmark {
    width: 8px;
    height: 8px;
    border: 1px solid #191820;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dots .item input {
    display: none;
}

.dots .item input:checked~.checkmark {
    width: 16px;
    height: 16px;
}

.dots .item input:checked~.checkmark::after {
    content: '';
    width: 8px;
    height: 8px;
    position: absolute;
    background: #191820;
    border-radius: 50%;
}

@media screen and (max-width: 1366px) {
    .container_review .main_review .detail_review {
        padding-right: 0px;
    }
}

@media screen and (max-width: 1168px) {
    .container_review .main_review .img_profile {
        width: 400px;
        height: 400px;
    }
    .container_review .main_review .detail_review {
        width: calc(100% - 400px);
    }
}

@media screen and (max-width: 968px) {
    .container_review {
        gap: 20px;
    }
    .container_review .main_review .img_profile {
        width: 300px;
        height: 300px;
    }
    .container_review .main_review .detail_review {
        width: calc(100% - 300px);
    }
    .container_review .parameter_review {
        flex-wrap: wrap;
        row-gap: 20px;
    }
    .container_review .parameter_review .item_parameter {
        width: 50%;
        gap: 4px;
    }
    .container_review .parameter_review .item_parameter:nth-child(2n) {
        padding-left: 28px;
    }
    .container_review .parameter_review .item_parameter:nth-child(2n+1) {
        padding-right: 28px;
        border-right: 1px solid var(--Outline);
    }
    .parameter_review .item_parameter .title {
        font-size: 32px;
        line-height: 40px;
    }
    .parameter_review .item_parameter .text {
        font-size: 16px;
        line-height: 26px;
    }
}

@media screen and (max-width: 768px) {
    .box_review {
        padding: 0;
    }
    .container_review {
        border-radius: unset;
        padding: 30px 20px;
        gap: 20px;
    }
    .container_review .main_review .img_profile {
        width: 300px;
        height: 300px;
    }
    .container_review .main_review .detail_review {
        padding: 20px 0px;
        width: 100%;
        gap: 20px;
    }
    .container_review .main_review {
        flex-direction: column;
        padding-bottom: 0;
    }
    .detail_review .head_review {
        gap: 4px;
    }
    .detail_review .head_review .sub_title {
        font-size: 12px;
        line-height: 18px;
    }
    .detail_review .head_review .title {
        font-size: 32px;
        line-height: 40px;
    }
    .detail_review .head_review .content {
        font-size: 16px;
        line-height: 26px;
    }
    .detail_review .content_review .text {
        font-size: 18px;
        line-height: 28px;
    }
    .box_profile .content_profile .name {
        font-size: 18px;
        line-height: 28px;
    }
    .box_profile .content_profile .position {
        font-size: 14px;
        line-height: 22px;
    }
    .box_profile .read_more {
        font-size: 14px;
        line-height: 22px;
    }
}

@media screen and (max-width: 768px) {
    .container_review .main_review .img_profile {
        width: fit-content;
        height: fit-content;
        max-width: 100%;
    }
}