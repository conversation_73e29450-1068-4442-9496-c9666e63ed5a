.box_events {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 100px 30px;
}

.container_box_events {
    width: 1290px;
    max-width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 30px;
}

.box_text {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 12px;
}

.box_text .sub_title {
    font-weight: 600;
    font-size: 14px;
    line-height: 22px;
    letter-spacing: 8%;
    color: var(--On-Surface);
}

.box_text .title {
    font-weight: 500;
    font-size: 44px;
    line-height: 52px;
    letter-spacing: 0%;
    color: var(--On-Surface);
}

.box_list_event {
    width: 100%;
    overflow: scroll;
    display: flex;
    justify-content: center;
}

.list_event {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.list_event .item_event {
    width: 410px;
    height: 447px;
    display: flex;
    flex-direction: column;
    border-radius: 12px;
    overflow: hidden;
}

.list_event .item_event .box_info {
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: 140px;
    background: var(--Surface);
    padding: 24px;
}

.list_event .item_event .name {
    font-weight: 500;
    font-size: 20px;
    line-height: 28px;
    color: var(--On-Surface);
}

.list_event .item_event .cat {
    font-weight: 400;
    font-size: 15px;
    line-height: 15px;
    text-transform: uppercase;
    color: var(--Secondary);
}

.list_event .item_event .list_info {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.list_event .item_event .row_info {
    display: flex;
    gap: 8px;
    align-items: center;
}

.list_event .item_event .row_info p {
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: var(--Secondary);
}

.list_event .item_event .box_img {
    width: 100%;
    height: calc(100% - 140px);
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
}

.list_event .item_event .box_img .date_sticker {
    position: absolute;
    left: 24px;
    bottom: 24px;
    width: 80px;
    height: 80px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    background: var(--On-Surface);
}

.list_event .item_event .box_img .sticker_day {
    font-weight: 500;
    font-size: 32px;
    line-height: 32px;
    text-align: center;
    color: var(--White);
}

.list_event .item_event .box_img .sticker_month {
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    text-align: center;
    color: var(--White);
}

.btn_load_more {
    padding: 14px 28px;
    border-radius: 40px;
    background: var(--Primary);
    font-weight: 600;
    font-size: 16px;
    line-height: 24px;
    letter-spacing: 0%;
    color: var(--White);
}

@media screen and (max-width: 1366px) {
    .box_events {
        padding: 40px 0px;
    }
    .box_text .sub_title {
        font-size: 12px;
        line-height: 18px;
    }
    .box_text .title {
        font-size: 32px;
        line-height: 40px;
    }
    .box_list_event {
        padding: 0px 16px;
    }
    .container_box_events {
        gap: 20px;
    }
    .list_event {
        width: 960px;
        gap: 30px;
    }
    .list_event .item_event {
        width: 300px;
        height: 337px;
        display: flex;
        flex-direction: column;
        border-radius: 12px;
        overflow: hidden;
    }
    .list_event .item_event .box_info {
        gap: 8px;
        height: 112px;
        padding: 12px;
    }
    .list_event .item_event .box_img {
        height: calc(100% - 112px);
    }
    .list_event .item_event .row_info p {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .btn_load_more {
        padding: 11px 28px;
        font-size: 14px;
        line-height: 22px;
    }
}

@media screen and (max-width: 990px) {
    .box_list_event {
        display: block;
        padding-bottom: 20px;
    }
}