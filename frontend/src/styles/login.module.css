.signup {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100vw;
    height: 100vh;
}

.error {
    color: red;
    font-size: 12px;
}

.main_signup {
    display: flex;
    flex-direction: column;
    width: 350px;
    max-width: 100%;
    border: 1px solid rgba(201, 201, 201, 0.23);
    box-shadow: rgba(202, 202, 202, 0.3) 0px 0px 8px 2px;
    padding: 20px 25px;
    border-radius: 5px;
}

.main_signup .head {
    font-family: Raleway;
    font-size: 22px;
    font-weight: bold;
    color: rgb(0, 0, 0);
    text-align: center;
}

.box_fg {
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
}

.box_fg .btn_login_by {
    font-family: Montserrat;
    font-size: 13px;
    width: 100%;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    color: #000;
    font-weight: bold;
    border-style: solid;
    border-width: 1px;
    border-color: rgba(201, 201, 201, 0.47);
}

.btn_login_by.facebook img {
    width: 27px;
    height: 27px;
}

.btn_login_by.google img {
    width: 25px;
    height: 25px;
}

.box_line {
    align-self: center;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 300px;
    margin: 20px 0;
}

.box_line .line {
    width: calc((100% - 25px) / 2);
    height: 1px;
    background: rgba(202, 202, 202, 0.5);
}

.box_line .text {
    width: 25px;
    text-align: center;
    font-family: Montserrat;
    font-size: 13px;
    font-weight: 400;
}

.row_input {
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
    flex-direction: column;
}

.row_input.first {
    margin-top: 30px;
}

.row_input input {
    border-style: solid;
    border-width: 2px;
    border-color: rgba(201, 201, 201, 0.25);
    border-radius: 2px;
    height: 45px;
    padding: 10px;
    width: 100%;
}

.btn_submit {
    width: 100%;
    margin-top: 10px;
    width: 100%;
    height: 45px;
    border: none;
    background: rgba(64, 193, 178, 1);
    font-family: Montserrat;
    font-size: 14px;
    font-weight: 600;
    color: rgb(255, 255, 255);
}

.box_demo {
    display: flex;
    gap: 5px;
    margin-top: 5px;
}

.box_demo button {
    width: calc((100% - 5px) / 2);
    height: 45px;
    font-family: 'Montserrat';
    font-size: 14px;
    font-weight: 600;
    color: rgba(255, 255, 255, 1);
    text-align: center;
    line-height: 1;
    border-radius: 3px;
    background: rgba(223, 157, 70, 1);
}

.btn_forgot {
    font-family: 'Montserrat';
    font-size: 13px;
    font-weight: 400;
    color: rgba(17, 79, 84, 1);
    text-align: right;
    margin-top: 2px;
}

.text_signup {
    align-self: center;
    font-family: Montserrat;
    font-size: 13px;
    font-weight: 400;
    color: #000;
    margin-bottom: 5px;
    margin: 20px 5px;
}

.box_login a {
    font-family: Montserrat;
    font-size: 13px;
    font-weight: 400;
    color: rgb(196, 148, 80);
    padding-left: 5px;
}

.btn_signup {
    font-family: 'Montserrat';
    font-size: 14px;
    font-weight: 600;
    color: rgba(24, 107, 96, 1);
    text-align: center;
    border: 2px solid rgba(24, 107, 96, 1);
    border-radius: 3px;
    width: 100%;
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.main_forgot {
    display: flex;
    flex-direction: column;
    width: 450px;
    max-width: 100%;
    border: 1px solid rgba(201, 201, 201, 0.23);
    box-shadow: rgba(202, 202, 202, 0.3) 0px 0px 8px 2px;
    padding: 25px 50px;
    border-radius: 5px;
}

.main_forgot .icon_forgot {
    align-self: center;
    width: 100px;
}

.inp_full {
    width: 100%;
    height: 45px;
}

.main_forgot .head {
    font-family: Raleway;
    font-size: 22px;
    font-weight: bold;
    color: rgb(0, 0, 0);
    text-align: center;
    margin-top: 10px;
    margin-bottom: 20px;
}

.main_forgot .text {
    font-family: 'Montserrat';
    font-size: 13px;
    font-weight: 400;
    color: rgba(0, 0, 0, 1);
    line-height: 1.5;
    margin-bottom: 5px;
}

.main_forgot .row_input {
    margin-top: 0px;
}

.main_forgot .box_btn {
    display: flex;
    gap: 10px;
}

.main_forgot .box_btn button {
    width: calc(50% - 5px);
    height: 45px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.main_forgot .box_btn button.cancel {
    font-family: 'Montserrat';
    font-size: 14px;
    font-weight: 600;
    color: rgba(24, 107, 96, 1);
    border: 1px solid rgba(24, 107, 96, 1);
    border-radius: 3px;
}

.main_forgot .box_btn button.send_mail {
    font-family: 'Montserrat';
    font-size: 14px;
    font-weight: 600;
    color: #fff;
    background: #40C1B2;
    border-radius: 3px;
}

@media screen and (max-width: 600px) {
    .box_fg {
        flex-direction: column;
    }
    .box_fg .btn_login_by {
        width: 100%;
    }
}