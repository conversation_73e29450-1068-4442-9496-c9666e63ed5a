import React, { useState } from "react";
import Breadcrumb from "../components/global/breadcrumb/Breadcrumb";
import Footer from "../components/global/footer/Footer";
import Header from "../components/global/header/Header";
import ImageDescription from "../components/Profile/ImageDescription";
import ProductsDetails from "../components/Profile/ProductsDetails";
import RatingReviews from "../components/Profile/RatingReviews";
import "../styles/profile/profile.css";
import { createFileRoute } from "@tanstack/react-router";

export const Route = createFileRoute("/profile")({
  component: Profile,
});

function Profile() {
  const contentDescription = [
    {
      name: "About",
      content:
        "Samantha is a spiritual coach and energy healer dedicated to helping individuals align with their true selves. With a deep connection to universal energies and years of experience, they offer transformative healing practices that support your mind, body, and spirit. Through ancient wisdom, energy healing, and intuitive guidance, <PERSON> helps you navigate your personal journey with clarity and empowerment.",
    },
    {
      name: "Services",
      content:
        "Energy Healing: Clearing blockages and restoring balance to your energy system. Spiritual Coaching: Guiding you through personal challenges, life transitions, and spiritual growth. Meditation & Mindfulness: Custom meditation sessions to quiet the mind and connect with your inner wisdom. Intuitive Readings: Offering insights into your current life path, relationships, and spiritual direction.",
    },
    {
      name: "Meet Our Psychics",
      content:
        "With a compassionate and intuitive approach, Samantha combines traditional healing methods with modern spiritual practices. They have helped countless individuals transform their lives by reconnecting with their higher selves and embracing their innate potential. Their passion for guiding others stems from a deep belief in the power of healing energy and the ability to manifest positive change.",
    },
    {
      name: "Reader Insight",
      content:
        "Samantha consistently receives praise for her intuitive guidance and transformative healing sessions. Clients describe feeling deeply understood, empowered, and aligned with their true selves after working with her, with many noting the profound shifts in their energy and spiritual clarity.",
    },
  ];
  const dataRelatedProducts = [
    {
      img: "/assets/images/events/image2.png",
      nameTags: "Mystic Minds",
      title: "Top Psychic Readers",
      countConsultations: 450,
      avatar: "/assets/images/profile/avatar1.png",
      name: "Ralph Edwards",
    },
    {
      img: "/assets/images/events/image3.png",
      nameTags: "Holistic Wellness",
      title: "Top Psychic Readers2",
      countConsultations: 380,
      avatar: "/assets/images/profile/avatar2.png",
      name: "Courtney Henry",
    },
    {
      img: "/assets/images/events/image4.png",
      nameTags: "Intuitive Guides",
      title: "Top Psychic Readers3",
      countConsultations: 213,
      avatar: "/assets/images/profile/avatar3.png",
      name: "Theresa Webb",
    },
    {
      img: "/assets/images/events/image5.png",
      nameTags: "Mystic Minds",
      title: "Top Psychic Readers4",
      countConsultations: 450,
      avatar: "/assets/images/profile/avatar4.png",
      name: "Darrell Steward",
    },
  ];
  const [isRatingReview, setIsRatingReview] = useState(false);
  return (
    <div>
      <Header />
      <div className="container_content_profile">
        <div className="container_Breadcrumb">
          <Breadcrumb />
        </div>
        <ImageDescription />
        <div className="container_description_rating_reviews">
          <div className="container_navbar_des_rate">
            <div
              className={
                !isRatingReview ? "container_select" : "container_no_select"
              }
              onClick={() => setIsRatingReview(false)}
            >
              <p className={!isRatingReview ? "text_select" : "text_no_select"}>
                Description
              </p>
            </div>
            <div
              className={
                isRatingReview ? "container_select" : "container_no_select"
              }
              onClick={() => setIsRatingReview(true)}
            >
              <p className={isRatingReview ? "text_select" : "text_no_select"}>
                Ratings & Reviews (3)
              </p>
            </div>
          </div>
          <div className="container_content_select">
            {isRatingReview ? (
              <>
                {/* Ratings & Reviews */}
                <RatingReviews />
              </>
            ) : (
              <>
                {/* Description */}
                <div className="container_content_description">
                  {contentDescription.map((item, index) => (
                    <div key={index} className="item_content_description">
                      <p className="text_description_name">{item.name}</p>
                      <p className="text_description_content">{item.content}</p>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
        <div className="container_related_products">
          <p className="text_related">Related Services</p>
          <div className="container_lists_products">
            {dataRelatedProducts.map((product, index) => (
              <div className="container_item_product" key={index}>
                <ProductsDetails product={product} />
              </div>
            ))}
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
}
