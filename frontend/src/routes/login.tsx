import { createFileRoute } from "@tanstack/react-router"
import {useEffect, useState} from "react"
import styles from "../styles/login.module.css";
import Cookies from "js-cookie";
import { useNavigate } from "@tanstack/react-router";
const API_URL = import.meta.env.VITE_API_URL || "http://13.54.166.15:8000";

export const Route = createFileRoute("/login")({
  component: Login,
})



function Login() {
  const navigate = useNavigate(); // For redirection
  const [showForgot, setShowForgot] = useState(false);
  const [isValid, setIsValid] = useState(true);
  const [msgMail, setMsgMail] = useState("");
  const [msgPass, setMsgPass] = useState("");
  const [mail, setMail] = useState("");
  const [pass, setPass] = useState("");
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [type, setType] = useState(0);
  const onChangeMail = (e:any) => {
    setMail(e.target.value);
  };
  const onChangePass = (e:any) => {
    setPass(e.target.value);
  };

  useEffect(() => {
    if (mail || pass) {
      validate();
    }
  }, [mail, pass]);

  const validMail = (mail:string) => {
    const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,8}$/;
    return emailPattern.test(mail);
  };

  const validPass = (pass:string) => {
    const hasUpperCase = /[A-Z]/.test(pass);
    const hasLowerCase = /[a-z]/.test(pass);
    const hasDigit = /[0-9]/.test(pass);
    const hasSpecialChar = /[!@#$%^&*()_+[\]{};':"\\|,.<>/?]+/.test(pass);
    const isLengthValid = pass.length >= 8;
    return hasUpperCase && hasLowerCase && hasDigit && isLengthValid && hasSpecialChar;
  };

  const validate = () => {
    let isEmail = validMail(mail);
    let isValidPass = validPass(pass);

    setMsgMail(isEmail ? "" : "Email is not in correct format");
    setMsgPass(
      isValidPass
        ? ""
        : "Password must contain at least 8 characters, including 1 lowercase letter, 1 uppercase letter, 1 number, and 1 symbol"
    );
    setIsValid(isEmail && isValidPass);
  };

  const handleLogin = async (e: any) => {
    e.preventDefault();
    setLoading(true);
    setErrorMsg("");

    try {
      const response = await fetch(`${API_URL}/api/v1/users/login`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email: mail, password: pass , type:type}),
      });

      const data = await response.json();
      setLoading(false);

      if (!response.ok) throw new Error(data.detail || "Login failed");

      // Save access token to cookies
      Cookies.set("access_token", data.access_token, { expires: 1, secure: true });
      // Redirect to homepage
      navigate({ to: "/" });
      alert("Login successful!"); // Replace with redirect logic
    } catch (error: any) {
      setLoading(false);
      setErrorMsg(error.message);
    }
  };
  
  return (
    <div className={styles.signup}>
        <form style={showForgot?{display:'none'}:{}} className={styles.main_signup}>
          <p className={styles.head}>Log in to NovaSeer</p>
          <div className={`${styles.row_input} ${styles.first}`}>
            <input onBlur={(e)=>{onChangeMail(e)}} type="text" placeholder="Email Address" className={styles.inp_full} />
            <p className={styles.error}>{msgMail}</p>
          </div>
          <div className={styles.row_input}>
            <input onBlur={(e)=>{onChangePass(e)}} type="password" placeholder="Password" className={styles.inp_full} />
            <p className={styles.error}>{msgPass}</p>
          </div>
          <div className={styles.row_input}>
            <select className={styles.inp_full} onChange={(e) => setType(Number(e.target.value))}>
              <option value={0}>User</option>
              <option value={1}>Seer</option>
            </select>
          </div>
          <button type="button" onClick={()=>{setShowForgot(true)}} className={styles.btn_forgot}>Forgot Password?</button>
          <button type="submit" onClick={(e:any)=> handleLogin(e) } disabled = {isValid?false:true} className={styles.btn_submit}>Login</button>
          <div className={styles.box_demo}>
            <button>Demo Healer</button>
            <button>Demo Seeker</button>
          </div>
          <div className={styles.box_line}>
              <div className={styles.line}></div>
              <div className={styles.text}>or</div>
              <div className={styles.line}></div>
          </div>
          <div className={styles.box_fg}>
            <button className={`${styles.btn_login_by} ${styles.google}`}>
              <img src="/assets/images/signup/icon_gg.png" alt="google" />Continue with Google
            </button>
            <button className={`${styles.btn_login_by} ${styles.facebook}`}>
              <img src="/assets/images/signup/icon_fb.png" alt="facebook" />Continue with Facebook
            </button>
            
          </div>
          <p className={styles.text_signup}>
            Don't have an account yet?
          </p>
          <a href="/signup" className={styles.btn_signup}>Sign Up</a>
        </form>
        <form style={!showForgot?{display:'none'}:{}} className={styles.main_forgot}>
          <img className={styles.icon_forgot} src="/assets/images/signup/icon_forgot.png" alt="forgot password" />
          <p className={styles.head}>Update your password</p>
          <p className={styles.text}>Enter your email address and select <strong>Send Email</strong></p>
          <div className={`${styles.row_input} ${styles.first}`}>
            {/* <input onBlur={(e)=>{onChangeMailForgot(e)}} type="text" placeholder="Email Address" className={styles.inp_full} />
            <p className={styles.error}>{msgMailForgot}</p> */}
          </div>
          <div className={styles.box_btn}>
            <button type="button" onClick={()=>{setShowForgot(false)}} className={styles.cancel}>Cancel</button>
            <button type="button" className={styles.send_mail}>Send Email</button>
          </div>
        </form>
    </div>
    
  )
}