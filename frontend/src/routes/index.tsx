import { createFileRoute } from "@tanstack/react-router"

import useAuth from "../hooks/useAuth"

import Header from "../components/global/header/Header"
import Footer from "../components/global/footer/Footer"
import Banner from "../components/Home/BannerHome"
import SlideExperts from "../components/Home/SlideExperts"
import BoxReason from "../components/Home/BoxReason"
import BannerSecond from "../components/Home/BannerHomeSecond"
import BoxEvents from "../components/Home/BoxEvents"
import BoxReview from "../components/Home/BoxReview"
import BoxFavourite from "../components/Home/BoxFavourite"

export const Route = createFileRoute("/")({
  component: Home,
})

function Home() {

  return (
    <>
      <Header/>
      <Banner/>
      <SlideExperts/>
      <BoxReason/>
      <BannerSecond/>
      <BoxEvents/>
      <BoxReview/>
      <BoxFavourite/>
      <Footer/>
    </>
  )
}