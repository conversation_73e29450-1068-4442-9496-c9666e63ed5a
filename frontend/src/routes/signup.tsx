import { createFileRoute } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import styles from "../styles/signUp.module.css";
import Cookies from "js-cookie";
import { useNavigate } from "@tanstack/react-router";

const API_URL = import.meta.env.VITE_API_URL || "http://13.54.166.15:8000";

export const Route = createFileRoute("/signup")({
  component: SignUp,
});

function SignUp() {
  const navigate = useNavigate();
  const [isValid, setIsValid] = useState(true);
  const [msgMail, setMsgMail] = useState("");
  const [msgPass, setMsgPass] = useState("");
  const [mail, setMail] = useState("");
  const [pass, setPass] = useState("");
  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [country, setCountry] = useState("");
  const [type, setType] = useState(0);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");

  useEffect(() => {
    if (mail || pass) {
      validate();
    }
  }, [mail, pass]);

  const validMail = (mail:string) => {
    const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,8}$/;
    return emailPattern.test(mail);
  };

  const validPass = (pass:string) => {
    const hasUpperCase = /[A-Z]/.test(pass);
    const hasLowerCase = /[a-z]/.test(pass);
    const hasDigit = /[0-9]/.test(pass);
    const hasSpecialChar = /[!@#$%^&*()_+[\]{};':"\\|,.<>/?]+/.test(pass);
    const isLengthValid = pass.length >= 8;
    return hasUpperCase && hasLowerCase && hasDigit && isLengthValid && hasSpecialChar;
  };

  const validate = () => {
    let isEmail = validMail(mail);
    let isValidPass = validPass(pass);

    setMsgMail(isEmail ? "" : "Email is not in correct format");
    setMsgPass(
      isValidPass
        ? ""
        : "Password must contain at least 8 characters, including 1 lowercase letter, 1 uppercase letter, 1 number, and 1 symbol"
    );
    setIsValid(isEmail && isValidPass);
  };

  const handleSignup = async (e:any) => {
    e.preventDefault();
    setLoading(true);
    setErrorMsg("");

    try {
      console.log(JSON.stringify({
        email: mail,
        password: pass,
        firstname: firstName,
        lastname: lastName,
        country,
        type,
      }))
      const response = await fetch(`${API_URL}/api/v1/users/signup`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          email: mail,
          password: pass,
          firstname: firstName,
          lastname: lastName,
          country,
          type,
        }),
      });

      const data = await response.json();
      setLoading(false);

      if (!response.ok) throw new Error(data.detail || "Signup failed");

      Cookies.set("access_token", data.access_token, { expires: 1, secure: true });
      navigate({ to: "/" });
      alert("Signup successful!");
    } catch (error: any) {
      setLoading(false);
      setErrorMsg(error.message);
    }
  };
  return (
    <div className={styles.signup}>
      <div className={styles.main_signup}>
        <p className={styles.head}>Sign up</p>
        <div className={styles.box_fg}>
          <button className={styles.btn_login_by}>
            <img src="/assets/images/signup/icon_fb.png" alt="facebook" />Continue with Facebook
          </button>
          <button className={styles.btn_login_by}>
            <img src="/assets/images/signup/icon_gg.png" alt="google" />Continue with Google
          </button>
        </div>
        <div className={styles.box_line}>
          <div className={styles.line}></div>
          <div className={styles.text}>or</div>
          <div className={styles.line}></div>
        </div>
        <div className={styles.row_input}>
          <input type="text" placeholder="First Name" className={styles.inp_haft} onChange={(e) => setFirstName(e.target.value)} />
          <input type="text" placeholder="Last Name" className={styles.inp_haft} onChange={(e) => setLastName(e.target.value)} />
        </div>
        <div className={styles.row_input}>
          <input type="text" placeholder="Email" className={styles.inp_full} onBlur={(e) => setMail(e.target.value)} />
          <p className={styles.error}>{msgMail}</p>
        </div>
        <div className={styles.row_input}>
          <input type="password" placeholder="Password" className={styles.inp_full} onBlur={(e) => setPass(e.target.value)} />
          <p className={styles.error}>{msgPass}</p>
        </div>
        <div className={styles.row_input}>
          <select className={styles.inp_full} onChange={(e) => setCountry(e.target.value)}>
            <option value="">Select Country</option>
            <option value="Viet Nam">Viet Nam</option>
            <option value="USA">USA</option>
            <option value="Singapore">Singapore</option>
            <option value="China">China</option>
            <option value="UK">UK</option>
            <option value="Canada">Canada</option>
          </select>
        </div>
        <div className={styles.row_input}>
          <select className={styles.inp_full} onChange={(e) => setType(Number(e.target.value))}>
            <option value={0}>User</option>
            <option value={1}>Seer</option>
          </select>
        </div>
        <div className={`${styles.row_check} ${styles.first}`}>
          <label className={styles.item}>
            <p>Send me emails with tips on how to find guidance that fits my needs</p>
            <input type="checkbox" name="check" />
            <div className={styles.checkmark}></div>
          </label>
        </div>
        <div className={styles.row_check}>
          <label className={styles.item}>
            <p>Yes, I understand and agree to the NovaSeer Terms of Service, including the User Agreement and Privacy Policy</p>
            <input type="checkbox" name="check" />
            <div className={styles.checkmark}></div>
          </label>
        </div>
        <button className={styles.btn_submit} onClick={handleSignup} disabled={!isValid || loading}>{loading ? "Signing Up..." : "Create Account"}</button>
        <p className={styles.box_login}>
          Already have an account?<a href="/login">Log in</a>
        </p>
        <p className={styles.box_login}>
          Looking for Client?<a href="/login">Join as a Healer</a>
        </p>
      </div>
    </div>

  )
}